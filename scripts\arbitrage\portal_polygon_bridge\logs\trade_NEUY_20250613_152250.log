2025-06-13 15:22:50,488 - trade_NEUY - ERROR - 执行线程出错: Cannot run the event loop while another loop is running
2025-06-13 15:22:50,489 - trade_NEUY - ERROR - Traceback (most recent call last):
  File "C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\scripts\arbitrage\portal_polygon_bridge\bridge_arb_executor.py", line 340, in execute_in_thread
    result = loop.run_until_complete(self.execute_buy())
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\asyncio\base_events.py", line 630, in run_until_complete
    self._check_running()
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\asyncio\base_events.py", line 591, in _check_running
    raise RuntimeError(
RuntimeError: Cannot run the event loop while another loop is running

