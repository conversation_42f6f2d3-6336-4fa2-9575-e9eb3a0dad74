2025-06-13 05:53:26,161 - trade_HDAO - INFO - HDAO 交易线程已启动
2025-06-13 05:53:26,161 - trade_HDAO - INFO - 开始执行 HDAO 买入操作
2025-06-13 05:53:26,161 - trade_HDAO - INFO - 链: polygon
2025-06-13 05:53:26,161 - trade_HDAO - INFO - 代币地址: 0x72928d5436ff65e57f72d5566dcd3baedc649a88
2025-06-13 05:53:26,161 - trade_HDAO - INFO - USDT数量: 300.0
2025-06-13 05:53:26,161 - trade_HDAO - INFO - 使用USDT精度: 6
2025-06-13 05:53:26,162 - trade_HDAO - INFO - 获取到代币精度: 18
2025-06-13 05:53:26,162 - trade_HDAO - INFO - 获取交易路由...
2025-06-13 05:53:29,522 - trade_HDAO - INFO - 买入交易详情:
2025-06-13 05:53:29,523 - trade_HDAO - INFO -   预期输出数量: 209542.97293310313 HDAO
2025-06-13 05:53:29,523 - trade_HDAO - INFO -   预估Gas成本: 0.005967160769758335 USD
2025-06-13 05:53:29,523 - trade_HDAO - INFO -   交易路由: [[{'pool': '0xbb98b3d2b18aef63a3178023a920971cf5f29be4', 'tokenIn': '0xc2132d05d31c914a87c6611c10748aeb04b58e8f', 'tokenOut': '0x7ceb23fd6bc0add59e62ac25578270cff1b9f619', 'swapAmount': '240000000', 'amountOut': '90551121450353889', 'exchange': 'uniswapv3', 'poolType': 'uniswapv3', 'poolExtra': {'swapFee': 500, 'priceLimit': '1461300573427867316570072651998408279850435624080'}, 'extra': {'nSqrtRx96': '4078143249679832332885495', 'ri': '05d4e434-4963-46d3-b151-51b163b53175'}}, {'pool': '0xb53f4e2f1e7a1b8b9d09d2f2739ac6753f5ba5cb', 'tokenIn': '0x7ceb23fd6bc0add59e62ac25578270cff1b9f619', 'tokenOut': '0x72928d5436ff65e57f72d5566dcd3baedc649a88', 'swapAmount': '90551121450353889', 'amountOut': '167416658983955803254001', 'exchange': 'balancer-v2-weighted', 'poolType': 'balancer-v2-weighted', 'poolExtra': {'vault': '0xba12222222228d8ba445958a75a0704d566bf2c8', 'poolId': '0xb53f4e2f1e7a1b8b9d09d2f2739ac6753f5ba5cb000200000000000000000137', 'tokenOutIndex': 0, 'blockNumber': 72692747, 'approvalAddress': '0xba12222222228d8ba445958a75a0704d566bf2c8'}, 'extra': {}}], [{'pool': '0x5520385bfcf07ec87c4c53a7d8d65595dff69fa4', 'tokenIn': '0xc2132d05d31c914a87c6611c10748aeb04b58e8f', 'tokenOut': '0x0d500b1d8e8ef31e21c99d1db9a6444d3adf1270', 'swapAmount': '60000000', 'amountOut': '282848674075460680534', 'exchange': 'woofi-v3', 'poolType': 'woofi-v21', 'poolExtra': {'BlockNumber': 0}, 'extra': {}}, {'pool': '0x479e1b71a702a595e19b6d5932cd5c863ab57ee0', 'tokenIn': '0x0d500b1d8e8ef31e21c99d1db9a6444d3adf1270', 'tokenOut': '0x7ceb23fd6bc0add59e62ac25578270cff1b9f619', 'swapAmount': '282848674075460680534', 'amountOut': '22636509096057606', 'exchange': 'quickswap-v3', 'poolType': 'algebra-v1', 'poolExtra': {'swapFee': 0, 'priceLimit': '4306310045'}, 'extra': {'Liquidity': '48259070891138205762734', 'GlobalState': {'price': '709066249758049111759831151', 'tick': -94328, 'feeZto': 889, 'feeOtz': 889, 'timepoint_index': 18010, 'community_fee_token0': 150, 'community_fee_token1': 150, 'unlocked': False}}}, {'pool': '0xb53f4e2f1e7a1b8b9d09d2f2739ac6753f5ba5cb', 'tokenIn': '0x7ceb23fd6bc0add59e62ac25578270cff1b9f619', 'tokenOut': '0x72928d5436ff65e57f72d5566dcd3baedc649a88', 'swapAmount': '22636509096057606', 'amountOut': '42126313949147325590467', 'exchange': 'balancer-v2-weighted', 'poolType': 'balancer-v2-weighted', 'poolExtra': {'vault': '0xba12222222228d8ba445958a75a0704d566bf2c8', 'poolId': '0xb53f4e2f1e7a1b8b9d09d2f2739ac6753f5ba5cb000200000000000000000137', 'tokenOutIndex': 0, 'blockNumber': 72692747, 'approvalAddress': '0xba12222222228d8ba445958a75a0704d566bf2c8'}, 'extra': {}}]]
2025-06-13 05:53:29,523 - trade_HDAO - INFO - 开始执行实际交易...
2025-06-13 05:53:29,523 - trade_HDAO - ERROR - 执行买入操作时出错: swap_tokens() got an unexpected keyword argument 'amount_in'
2025-06-13 05:53:29,523 - trade_HDAO - ERROR - Traceback (most recent call last):
  File "C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\scripts\arbitrage\portal_polygon_bridge\bridge_arb_executor.py", line 199, in execute_buy
    swap_result = await swap_tokens(
                        ^^^^^^^^^^^^
TypeError: swap_tokens() got an unexpected keyword argument 'amount_in'

