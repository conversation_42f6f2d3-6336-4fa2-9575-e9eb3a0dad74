2025-06-13 03:52:06,606 - trade_GLQ - INFO - GLQ 交易线程已启动
2025-06-13 03:52:06,606 - trade_GLQ - INFO - 开始执行 GLQ 买入操作
2025-06-13 03:52:06,607 - trade_GLQ - INFO - 链: ethereum
2025-06-13 03:52:06,607 - trade_GLQ - INFO - 代币地址: ******************************************
2025-06-13 03:52:06,607 - trade_GLQ - INFO - USDT数量: 300.0
2025-06-13 03:52:06,607 - trade_GLQ - INFO - 使用USDT精度: 6
2025-06-13 03:52:08,015 - trade_GLQ - INFO - 获取到代币精度: 18
2025-06-13 03:52:08,015 - trade_GLQ - INFO - 获取交易路由...
2025-06-13 03:52:10,125 - trade_GLQ - INFO - 买入交易详情:
2025-06-13 03:52:10,126 - trade_GLQ - INFO -   预期输出数量: 13244.58719932964 GLQ
2025-06-13 03:52:10,126 - trade_GLQ - INFO -   预估Gas成本: 1.960734509907982 USD
2025-06-13 03:52:10,126 - trade_GLQ - INFO -   交易路由: [[{'pool': '******************************************', 'tokenIn': '******************************************', 'tokenOut': '******************************************', 'swapAmount': '300000000', 'amountOut': '111211097406029209', 'exchange': 'uniswapv3', 'poolType': 'uniswapv3', 'poolExtra': {'swapFee': 100, 'priceLimit': '1461446703485210103287273052203988822378723970341'}, 'extra': {'nSqrtRx96': '4114800010259142852376194', 'ri': '3868199a-5b1d-4574-89e6-e9e58375d145'}}, {'pool': '0xc3881fbb90daf3066da30016d578ed024027317c', 'tokenIn': '******************************************', 'tokenOut': '******************************************', 'swapAmount': '111211097406029209', 'amountOut': '13244587199329639762872', 'exchange': 'uniswapv3', 'poolType': 'uniswapv3', 'poolExtra': {'swapFee': 10000, 'priceLimit': '1456195216270955103206513029158776779468408838534'}, 'extra': {'nSqrtRx96': '228501218330731926181187857'}}]]
2025-06-13 03:52:10,126 - trade_GLQ - INFO - 开始执行实际交易...
2025-06-13 03:52:10,126 - trade_GLQ - ERROR - 执行买入操作时出错: swap_tokens() got an unexpected keyword argument 'amount_in'
2025-06-13 03:52:10,127 - trade_GLQ - ERROR - Traceback (most recent call last):
  File "C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\scripts\arbitrage\portal_polygon_bridge\bridge_arb_executor.py", line 199, in execute_buy
    swap_result = await swap_tokens(
                        ^^^^^^^^^^^^
TypeError: swap_tokens() got an unexpected keyword argument 'amount_in'

