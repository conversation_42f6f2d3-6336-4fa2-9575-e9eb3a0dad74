2025-06-13 14:44:13,903 - trade_NEUY - INFO - NEUY 交易线程已启动
2025-06-13 14:44:13,903 - trade_NEUY - INFO - ================================================================================
2025-06-13 14:44:13,905 - trade_NEUY - INFO - 开始执行 NEUY 买入交易 - 时间: 2025-06-13 14:44:13
2025-06-13 14:44:13,905 - trade_NEUY - INFO - 链: ethereum, 投入金额: 300.0 USDT
2025-06-13 14:44:13,905 - trade_NEUY - INFO - 代币地址: ******************************************
2025-06-13 14:44:13,905 - trade_NEUY - INFO - NEUY: 将在ethereum链上执行买入，代币地址: ******************************************
2025-06-13 14:44:13,906 - trade_NEUY - INFO - 使用USDT精度: 6
2025-06-13 14:44:15,433 - trade_NEUY - INFO - 获取到代币精度: 18
2025-06-13 14:44:15,433 - trade_NEUY - INFO - 获取交易路由...
2025-06-13 14:44:20,720 - trade_NEUY - INFO - 买入交易详情:
2025-06-13 14:44:20,721 - trade_NEUY - INFO -   预期输出数量: 10078.515011473524 NEUY
2025-06-13 14:44:20,721 - trade_NEUY - INFO -   预估Gas成本: 0.8016442262377477 USD
2025-06-13 14:44:20,722 - trade_NEUY - INFO -   交易路由: [[{'pool': '0x317ceb54f44a474210b11244df14a650f717683d0aa6ddfe77dc0895e53dc660', 'tokenIn': '******************************************', 'tokenOut': '******************************************', 'swapAmount': '300000000', 'amountOut': '119819484514454050', 'exchange': 'ekubo', 'poolType': 'ekubo', 'poolExtra': {'core': '******************************************', 'poolKey': {'token0': '0x0000000000000000000000000000000000000000', 'token1': '******************************************', 'config': [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 19, 169, 42, 48, 85, 50, 97, 0, 0, 19, 126]}}, 'extra': {'isToken1': True, 'ri': '27253cda-5396-4ebd-bffd-f1c83ba44ea8', 'skipAhead': 0}}, {'pool': '0xa3ad7059a17a3624c00911c4ccca285f93943e05', 'tokenIn': '******************************************', 'tokenOut': '******************************************', 'swapAmount': '119819484514454050', 'amountOut': '10078515011473524916032', 'exchange': 'uniswapv3', 'poolType': 'uniswapv3', 'poolExtra': {'swapFee': 10000, 'priceLimit': '333739444973130141886313358'}, 'extra': {'nSqrtRx96': '272871053958602357702752133'}}]]
2025-06-13 14:44:20,722 - trade_NEUY - INFO - 开始执行实际交易...
2025-06-13 14:44:20,736 - trade_NEUY - ERROR - 交易执行失败: 处理代币地址时出错: Exceeds the limit (4300 digits) for integer string conversion: value has 9000000 digits; use sys.set_int_max_str_digits() to increase the limit
