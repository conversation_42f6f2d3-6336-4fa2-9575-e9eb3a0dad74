/*! For license information please see web3-validator.min.js.LICENSE.txt */
!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports["web3-validator"]=t():e["web3-validator"]=t()}(this,(()=>(()=>{"use strict";var e={5676:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.VALID_ETH_BASE_TYPES=void 0,t.VALID_ETH_BASE_TYPES=["bool","int","uint","bytes","string","address","tuple"]},889:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.validator=void 0;const s=r(4894);t.validator=new s.Web3Validator},7732:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Web3ValidatorError=void 0;const s=r(5071),n=e=>e.message?e.message:"unspecified error";class o extends s.BaseWeb3Error{constructor(e){super(),this.code=s.ERR_VALIDATION,this.errors=e,super.message=`Web3 validator found ${e.length} error[s]:\n${this._compileErrors().join("\n")}`}_compileErrors(){return this.errors.map(n)}}t.Web3ValidatorError=o},219:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});const s=r(3994),n=r(4988),o=r(6761),a=r(5500),i=r(5456),d=r(8618),c=r(4683),u=r(2397),l={address:e=>(0,s.isAddress)(e),bloom:e=>(0,o.isBloom)(e),blockNumber:e=>(0,n.isBlockNumber)(e),blockTag:e=>(0,n.isBlockTag)(e),blockNumberOrTag:e=>(0,n.isBlockNumberOrTag)(e),bool:e=>(0,a.isBoolean)(e),bytes:e=>(0,i.isBytes)(e),filter:e=>(0,d.isFilterObject)(e),hex:e=>(0,c.isHexStrict)(e),uint:e=>(0,u.isUInt)(e),int:e=>(0,u.isInt)(e),number:e=>(0,u.isNumber)(e),string:e=>(0,c.isString)(e)};for(let e=8;e<=256;e+=8)l[`int${e}`]=t=>(0,u.isInt)(t,{bitSize:e}),l[`uint${e}`]=t=>(0,u.isUInt)(t,{bitSize:e});for(let e=1;e<=32;e+=1)l[`bytes${e}`]=t=>(0,i.isBytes)(t,{size:e});l.bytes256=l.bytes,t.default=l},6097:function(e,t,r){var s=this&&this.__createBinding||(Object.create?function(e,t,r,s){void 0===s&&(s=r);var n=Object.getOwnPropertyDescriptor(t,r);n&&!("get"in n?!t.__esModule:n.writable||n.configurable)||(n={enumerable:!0,get:function(){return t[r]}}),Object.defineProperty(e,s,n)}:function(e,t,r,s){void 0===s&&(s=r),e[s]=t[r]}),n=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__exportStar||function(e,t){for(var r in e)"default"===r||Object.prototype.hasOwnProperty.call(t,r)||s(t,e,r)},a=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)"default"!==r&&Object.prototype.hasOwnProperty.call(e,r)&&s(t,e,r);return n(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.utils=void 0,o(r(4894),t),o(r(889),t),o(r(3378),t),t.utils=a(r(1666)),o(r(7732),t),o(r(5676),t),o(r(6093),t)},3378:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0})},1666:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ensureIfUint8Array=t.hexToUint8Array=t.uint8ArrayToHexString=t.padLeft=t.numberToHex=t.hexToNumber=t.codePointToInt=t.transformJsonDataToAbiFormat=t.fetchArrayElement=t.ethAbiToJsonSchema=t.abiSchemaToJsonSchema=t.parseBaseType=void 0;const s=r(5071),n=r(5676),o=r(1512),a=r(4683),i=r(7732),d=["hex","number","blockNumber","blockNumberOrTag","filter","bloom"];t.parseBaseType=e=>{let t,r=e.replace(/ /,""),s=!1,o=[];if(e.includes("[")&&(r=r.slice(0,r.indexOf("[")),o=[...e.matchAll(/(?:\[(\d*)\])/g)].map((e=>parseInt(e[1],10))).map((e=>Number.isNaN(e)?-1:e)),s=o.length>0),n.VALID_ETH_BASE_TYPES.includes(r))return{baseType:r,isArray:s,baseTypeSize:t,arraySizes:o};if(r.startsWith("int"))t=parseInt(r.substring(3),10),r="int";else if(r.startsWith("uint"))t=parseInt(e.substring(4),10),r="uint";else{if(!r.startsWith("bytes"))return{baseType:void 0,isArray:!1,baseTypeSize:void 0,arraySizes:o};t=parseInt(r.substring(5),10),r="bytes"}return{baseType:r,isArray:s,baseTypeSize:t,arraySizes:o}};const c=(e,r={})=>{if(Object.keys(r).includes("type"))throw new i.Web3ValidatorError([{keyword:"eth",message:'Either "eth" or "type" can be presented in schema',params:{eth:e},instancePath:"",schemaPath:""}]);const{baseType:s,baseTypeSize:n}=(0,t.parseBaseType)(e);if(!s&&!d.includes(e))throw new i.Web3ValidatorError([{keyword:"eth",message:`Eth data type "${e}" is not valid`,params:{eth:e},instancePath:"",schemaPath:""}]);if(s){if("tuple"===s)throw new Error('"tuple" type is not implemented directly.');return{format:`${s}${null!=n?n:""}`,required:!0}}return e?{format:e,required:!0}:{}};t.abiSchemaToJsonSchema=(e,r="/0")=>{const s={type:"array",items:[],maxItems:e.length,minItems:e.length};for(const[n,a]of e.entries()){let e,i,d=[];(0,o.isAbiParameterSchema)(a)?(e=a.type,i=a.name||`${r}/${n}`,d=a.components):"string"==typeof a?(e=a,i=`${r}/${n}`):Array.isArray(a)&&(a[0]&&"string"==typeof a[0]&&a[0].startsWith("tuple")&&!Array.isArray(a[0])&&a[1]&&Array.isArray(a[1])?(e=a[0],i=`${r}/${n}`,d=a[1]):(e="tuple",i=`${r}/${n}`,d=a));const{baseType:u,isArray:l,arraySizes:_}=(0,t.parseBaseType)(e);let p,h=s;for(let e=_.length-1;e>0;e-=1)p={type:"array",$id:i,items:[],maxItems:_[e],minItems:_[e]},_[e]<0&&(delete p.maxItems,delete p.minItems),Array.isArray(h.items)?0===h.items.length?h.items=[p]:h.items.push(p):h.items=[h.items,p],h=p;if("tuple"!==u||l)if("tuple"===u&&l){const e=_[0],r=Object.assign({type:"array",$id:i,items:(0,t.abiSchemaToJsonSchema)(d,i)},e>=0&&{minItems:e,maxItems:e});h.items.push(r)}else if(l){const t=_[0],r=Object.assign({type:"array",$id:i,items:c(e)},t>=0&&{minItems:t,maxItems:t});h.items.push(r)}else Array.isArray(h.items),h.items.push(Object.assign({$id:i},c(e)));else{const e=(0,t.abiSchemaToJsonSchema)(d,i);e.$id=i,h.items.push(e)}h=s}return s},t.ethAbiToJsonSchema=e=>(0,t.abiSchemaToJsonSchema)(e),t.fetchArrayElement=(e,r)=>1===r?e:(0,t.fetchArrayElement)(e[0],r-1),t.transformJsonDataToAbiFormat=(e,r,s)=>{const n=[];for(const[a,i]of e.entries()){let e,d,c=[];(0,o.isAbiParameterSchema)(i)?(e=i.type,d=i.name,c=i.components):"string"==typeof i?e=i:Array.isArray(i)&&(i[1]&&Array.isArray(i[1])?(e=i[0],c=i[1]):(e="tuple",c=i));const{baseType:u,isArray:l,arraySizes:_}=(0,t.parseBaseType)(e),p=Array.isArray(r)?r[a]:r[d];if("tuple"!==u||l)if("tuple"===u&&l){const e=[];for(const r of p)if(_.length>1){const n=(0,t.fetchArrayElement)(r,_.length-1),o=[];for(const e of n)o.push((0,t.transformJsonDataToAbiFormat)(c,e,s));e.push(o)}else e.push((0,t.transformJsonDataToAbiFormat)(c,r,s));n.push(e)}else n.push(p);else n.push((0,t.transformJsonDataToAbiFormat)(c,p,s))}return(s=null!=s?s:[]).push(...n),s},t.codePointToInt=e=>{if(e>=48&&e<=57)return e-48;if(e>=65&&e<=70)return e-55;if(e>=97&&e<=102)return e-87;throw new Error(`Invalid code point: ${e}`)},t.hexToNumber=e=>{if(!(0,a.isHexStrict)(e))throw new Error("Invalid hex string");const[t,r]=e.startsWith("-")?[!0,e.slice(1)]:[!1,e],s=BigInt(r);return s>Number.MAX_SAFE_INTEGER?t?-s:s:s<Number.MIN_SAFE_INTEGER?s:t?-1*Number(s):Number(s)},t.numberToHex=e=>{if(("number"==typeof e||"bigint"==typeof e)&&e<0)return`-0x${e.toString(16).slice(1)}`;if(("number"==typeof e||"bigint"==typeof e)&&e>=0)return`0x${e.toString(16)}`;if("string"==typeof e&&(0,a.isHexStrict)(e)){const[t,r]=e.startsWith("-")?[!0,e.slice(1)]:[!1,e];return`${t?"-":""}0x${r.split(/^(-)?0(x|X)/).slice(-1)[0].replace(/^0+/,"").toLowerCase()}`}if("string"==typeof e&&!(0,a.isHexStrict)(e))return(0,t.numberToHex)(BigInt(e));throw new s.InvalidNumberError(e)},t.padLeft=(e,r,s="0")=>{if("string"==typeof e&&!(0,a.isHexStrict)(e))return e.padStart(r,s);const n="string"==typeof e&&(0,a.isHexStrict)(e)?e:(0,t.numberToHex)(e),[o,i]=n.startsWith("-")?["-0x",n.slice(3)]:["0x",n.slice(2)];return`${o}${i.padStart(r,s)}`},t.uint8ArrayToHexString=function(e){let t="0x";for(const r of e){const e=r.toString(16);t+=1===e.length?`0${e}`:e}return t};function u(e){return e>=48&&e<=57?e-48:e>=65&&e<=70?e-55:e>=97&&e<=102?e-87:void 0}t.hexToUint8Array=function(e){let t=0;if(!e.startsWith("0")||"x"!==e[1]&&"X"!==e[1]||(t=2),e.length%2!=0)throw new s.InvalidBytesError(`hex string has odd length: ${e}`);const r=(e.length-t)/2,n=new Uint8Array(r);for(let o=0,a=t;o<r;o+=1){const t=u(e.charCodeAt(a++)),r=u(e.charCodeAt(a++));if(void 0===t||void 0===r)throw new s.InvalidBytesError(`Invalid byte sequence ("${e[a-2]}${e[a-1]}" in "${e}").`);n[o]=16*t+r}return n},t.ensureIfUint8Array=function(e){var t;return e instanceof Uint8Array||"Uint8Array"!==(null===(t=null==e?void 0:e.constructor)||void 0===t?void 0:t.name)?e:Uint8Array.from(e)}},1512:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.isAbiParameterSchema=void 0,t.isAbiParameterSchema=e=>"object"==typeof e&&"type"in e&&"name"in e},3994:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.isAddress=t.checkAddressCheckSum=void 0;const s=r(2192),n=r(8040),o=r(1666),a=r(4683),i=r(5456);t.checkAddressCheckSum=e=>{if(!/^(0x)?[0-9a-f]{40}$/i.test(e))return!1;const t=e.slice(2),r=(0,n.utf8ToBytes)(t.toLowerCase()),a=(0,o.uint8ArrayToHexString)((0,s.keccak256)((0,o.ensureIfUint8Array)(r))).slice(2);for(let e=0;e<40;e+=1)if(parseInt(a[e],16)>7&&t[e].toUpperCase()!==t[e]||parseInt(a[e],16)<=7&&t[e].toLowerCase()!==t[e])return!1;return!0},t.isAddress=(e,r=!0)=>{if("string"!=typeof e&&!(0,i.isUint8Array)(e))return!1;let s;return s=(0,i.isUint8Array)(e)?(0,o.uint8ArrayToHexString)(e):"string"!=typeof e||(0,a.isHexStrict)(e)||e.toLowerCase().startsWith("0x")?e:`0x${e}`,!!/^(0x)?[0-9a-f]{40}$/i.test(s)&&(!(!/^(0x|0X)?[0-9a-f]{40}$/.test(s)&&!/^(0x|0X)?[0-9A-F]{40}$/.test(s))||!r||(0,t.checkAddressCheckSum)(s))}},4988:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.isBlockNumberOrTag=t.isBlockTag=t.isBlockNumber=void 0;const s=r(9970),n=r(2397);t.isBlockNumber=e=>(0,n.isUInt)(e),t.isBlockTag=e=>Object.values(s.BlockTags).includes(e),t.isBlockNumberOrTag=e=>(0,t.isBlockTag)(e)||(0,t.isBlockNumber)(e)},6761:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.isContractAddressInBloom=t.isUserEthereumAddressInBloom=t.isInBloom=t.isBloom=void 0;const s=r(2192),n=r(1666),o=r(3994),a=r(4683);t.isBloom=e=>!("string"!=typeof e||!/^(0x)?[0-9a-f]{512}$/i.test(e)||!/^(0x)?[0-9a-f]{512}$/.test(e)&&!/^(0x)?[0-9A-F]{512}$/.test(e)),t.isInBloom=(e,r)=>{if("string"==typeof r&&!(0,a.isHexStrict)(r))return!1;if(!(0,t.isBloom)(e))return!1;const o="string"==typeof r?(0,n.hexToUint8Array)(r):r,i=(0,n.uint8ArrayToHexString)((0,s.keccak256)(o)).slice(2);for(let t=0;t<12;t+=4){const r=(parseInt(i.slice(t,t+2),16)<<8)+parseInt(i.slice(t+2,t+4),16)&2047,s=1<<r%4;if(((0,n.codePointToInt)(e.charCodeAt(e.length-1-Math.floor(r/4)))&s)!==s)return!1}return!0},t.isUserEthereumAddressInBloom=(e,r)=>{if(!(0,t.isBloom)(e))return!1;if(!(0,o.isAddress)(r))return!1;const s=(0,n.padLeft)(r,64);return(0,t.isInBloom)(e,s)},t.isContractAddressInBloom=(e,r)=>!!(0,t.isBloom)(e)&&!!(0,o.isAddress)(r)&&(0,t.isInBloom)(e,r)},5500:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.isBoolean=void 0;const s=r(4683);t.isBoolean=e=>!!["number","string","boolean"].includes(typeof e)&&("boolean"==typeof e||("string"!=typeof e||(0,s.isHexStrict)(e)?"string"==typeof e&&(0,s.isHexStrict)(e)?"0x1"===e||"0x0"===e:1===e||0===e:"1"===e||"0"===e))},5456:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.isBytes=t.isUint8Array=void 0;const s=r(1666),n=r(4683);t.isUint8Array=e=>{var t,r;return e instanceof Uint8Array||"Uint8Array"===(null===(t=null==e?void 0:e.constructor)||void 0===t?void 0:t.name)||"Buffer"===(null===(r=null==e?void 0:e.constructor)||void 0===r?void 0:r.name)},t.isBytes=(e,r={abiType:"bytes"})=>{if("string"!=typeof e&&!Array.isArray(e)&&!(0,t.isUint8Array)(e))return!1;if("string"==typeof e&&(0,n.isHexStrict)(e)&&e.startsWith("-"))return!1;if("string"==typeof e&&!(0,n.isHexStrict)(e))return!1;let o;if("string"==typeof e){if(e.length%2!=0)return!1;o=(0,s.hexToUint8Array)(e)}else if(Array.isArray(e)){if(e.some((e=>e<0||e>255||!Number.isInteger(e))))return!1;o=new Uint8Array(e)}else o=e;if(null==r?void 0:r.abiType){const{baseTypeSize:e}=(0,s.parseBaseType)(r.abiType);return!e||o.length===e}return!(null==r?void 0:r.size)||o.length===(null==r?void 0:r.size)}},9363:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.isValidEthBaseType=void 0;const s=r(1666);t.isValidEthBaseType=e=>{const{baseType:t,baseTypeSize:r}=(0,s.parseBaseType)(e);return!!t&&(t===e||("int"!==t&&"uint"!==t||!r||r<=256&&r%8==0)&&("bytes"!==t||!r||r>=1&&r<=32))}},8618:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.isFilterObject=void 0;const s=r(3994),n=r(4988),o=r(9341),a=r(7643);t.isFilterObject=e=>{const t=["fromBlock","toBlock","address","topics","blockHash"];if((0,o.isNullish)(e)||"object"!=typeof e)return!1;if(!Object.keys(e).every((e=>t.includes(e))))return!1;if(!(0,o.isNullish)(e.fromBlock)&&!(0,n.isBlockNumberOrTag)(e.fromBlock)||!(0,o.isNullish)(e.toBlock)&&!(0,n.isBlockNumberOrTag)(e.toBlock))return!1;if(!(0,o.isNullish)(e.address))if(Array.isArray(e.address)){if(!e.address.every((e=>(0,s.isAddress)(e))))return!1}else if(!(0,s.isAddress)(e.address))return!1;return!(!(0,o.isNullish)(e.topics)&&!e.topics.every((e=>!!(0,o.isNullish)(e)||(Array.isArray(e)?e.every((e=>(0,a.isTopic)(e))):!!(0,a.isTopic)(e)))))}},6093:function(e,t,r){var s=this&&this.__createBinding||(Object.create?function(e,t,r,s){void 0===s&&(s=r);var n=Object.getOwnPropertyDescriptor(t,r);n&&!("get"in n?!t.__esModule:n.writable||n.configurable)||(n={enumerable:!0,get:function(){return t[r]}}),Object.defineProperty(e,s,n)}:function(e,t,r,s){void 0===s&&(s=r),e[s]=t[r]}),n=this&&this.__exportStar||function(e,t){for(var r in e)"default"===r||Object.prototype.hasOwnProperty.call(t,r)||s(t,e,r)};Object.defineProperty(t,"__esModule",{value:!0}),n(r(3994),t),n(r(4988),t),n(r(6761),t),n(r(5500),t),n(r(5456),t),n(r(9363),t),n(r(8618),t),n(r(2397),t),n(r(4683),t),n(r(7643),t),n(r(9341),t)},2397:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.isNumber=t.isInt=t.isUInt=t.bigintPower=t.isBigInt=void 0;const s=r(1666),n=r(4683);t.isBigInt=e=>"bigint"==typeof e,t.bigintPower=(e,t)=>{if(t===BigInt(0))return BigInt(1);let r=e;for(let s=1;s<t;s+=1)r*=e;return r},t.isUInt=(e,r={abiType:"uint"})=>{if(!["number","string","bigint"].includes(typeof e)||"string"==typeof e&&0===e.length)return!1;let o;if(null==r?void 0:r.abiType){const{baseTypeSize:e}=(0,s.parseBaseType)(r.abiType);e&&(o=e)}else r.bitSize&&(o=r.bitSize);const a=(0,t.bigintPower)(BigInt(2),BigInt(null!=o?o:256))-BigInt(1);try{const t="string"==typeof e&&(0,n.isHexStrict)(e)?BigInt((0,s.hexToNumber)(e)):BigInt(e);return t>=0&&t<=a}catch(e){return!1}},t.isInt=(e,r={abiType:"int"})=>{if(!["number","string","bigint"].includes(typeof e))return!1;if("number"==typeof e&&e>Number.MAX_SAFE_INTEGER)return!1;let o;if(null==r?void 0:r.abiType){const{baseTypeSize:e,baseType:t}=(0,s.parseBaseType)(r.abiType);if("int"!==t)return!1;e&&(o=e)}else r.bitSize&&(o=r.bitSize);const a=(0,t.bigintPower)(BigInt(2),BigInt((null!=o?o:256)-1)),i=BigInt(-1)*(0,t.bigintPower)(BigInt(2),BigInt((null!=o?o:256)-1));try{const t="string"==typeof e&&(0,n.isHexStrict)(e)?BigInt((0,s.hexToNumber)(e)):BigInt(e);return t>=i&&t<=a}catch(e){return!1}},t.isNumber=e=>!!(0,t.isInt)(e)||!("string"!=typeof e||!/[0-9.]/.test(e)||e.indexOf(".")!==e.lastIndexOf("."))||"number"==typeof e},9341:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.isObject=t.isNullish=void 0;const s=r(9970);t.isNullish=e=>null==e,t.isObject=e=>!("object"!=typeof e||(0,t.isNullish)(e)||Array.isArray(e)||e instanceof s.TypedArray)},4683:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.validateNoLeadingZeroes=t.isHexPrefixed=t.isHexString32Bytes=t.isHexString8Bytes=t.isHex=t.isHexString=t.isHexStrict=t.isString=void 0,t.isString=e=>"string"==typeof e,t.isHexStrict=e=>"string"==typeof e&&/^((-)?0x[0-9a-f]+|(0x))$/i.test(e),t.isHexString=function(e,t){return!("string"!=typeof e||!e.match(/^0x[0-9A-Fa-f]*$/)||void 0!==t&&t>0&&e.length!==2+2*t)},t.isHex=e=>"number"==typeof e||"bigint"==typeof e||"string"==typeof e&&/^((-0x|0x|-)?[0-9a-f]+|(0x))$/i.test(e),t.isHexString8Bytes=(e,r=!0)=>r?(0,t.isHexStrict)(e)&&18===e.length:(0,t.isHex)(e)&&16===e.length,t.isHexString32Bytes=(e,r=!0)=>r?(0,t.isHexStrict)(e)&&66===e.length:(0,t.isHex)(e)&&64===e.length,t.isHexPrefixed=function(e){if("string"!=typeof e)throw new Error("[isHexPrefixed] input must be type 'string', received type "+typeof e);return e.startsWith("0x")},t.validateNoLeadingZeroes=function(e){for(const[t,r]of Object.entries(e))if(void 0!==r&&r.length>0&&0===r[0])throw new Error(`${t} cannot have leading zeroes, received: ${r.toString()}`)}},7643:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.isTopicInBloom=t.isTopic=void 0;const s=r(6761);t.isTopic=e=>!("string"!=typeof e||!/^(0x)?[0-9a-f]{64}$/i.test(e)||!/^(0x)?[0-9a-f]{64}$/.test(e)&&!/^(0x)?[0-9A-F]{64}$/.test(e)),t.isTopicInBloom=(e,r)=>!!(0,s.isBloom)(e)&&!!(0,t.isTopic)(r)&&(0,s.isInBloom)(e,r)},5191:function(e,t,r){var s=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.Validator=void 0;const n=r(5071),o=r(6750),a=r(7732),i=s(r(219)),d=e=>{if((!(null==e?void 0:e.type)||"object"===(null==e?void 0:e.type))&&(null==e?void 0:e.properties)){const t={};for(const r of Object.keys(e.properties)){const s=d(e.properties[r]);s&&(t[r]=s)}return Array.isArray(e.required)?o.z.object(t).partial().required(e.required.reduce(((e,t)=>Object.assign(Object.assign({},e),{[t]:!0})),{})):o.z.object(t).partial()}if("array"===(null==e?void 0:e.type)&&(null==e?void 0:e.items)){if(Array.isArray(e.items)&&e.items.length>1&&void 0!==e.maxItems&&new Set(e.items.map((e=>e.$id))).size===e.items.length){const t=[];for(const r of e.items){const e=d(r);e&&t.push(e)}return o.z.tuple(t)}const t=Array.isArray(e.items)?e.items[0]:e.items;let r=o.z.array(d(t));return r=void 0!==e.minItems?r.min(e.minItems):r,r=void 0!==e.maxItems?r.max(e.maxItems):r,r}if(e.oneOf&&Array.isArray(e.oneOf))return o.z.union(e.oneOf.map((e=>d(e))));if(null==e?void 0:e.format){if(!i.default[e.format])throw new n.SchemaFormatError(e.format);return o.z.any().refine(i.default[e.format],(t=>({params:{value:t,format:e.format}})))}return(null==e?void 0:e.type)&&"object"!==(null==e?void 0:e.type)&&"function"==typeof o.z[String(e.type)]?o.z[String(e.type)]():o.z.object({data:o.z.any()}).partial()};class c{static factory(){return c.validatorInstance||(c.validatorInstance=new c),c.validatorInstance}validate(e,t,r){var s,n;const o=d(e).safeParse(t);if(!o.success){const e=this.convertErrors(null!==(n=null===(s=o.error)||void 0===s?void 0:s.issues)&&void 0!==n?n:[]);if(e){if(null==r?void 0:r.silent)return e;throw new a.Web3ValidatorError(e)}}}convertErrors(e){if(e&&Array.isArray(e)&&e.length>0)return e.map((e=>{var t;let r,s,n,a;a=e.path.join("/");const i=String(e.path[e.path.length-1]),d=e.path.join("/");if(e.code===o.ZodIssueCode.too_big)s="maxItems",a=`${d}/maxItems`,n={limit:e.maximum},r=`must NOT have more than ${e.maximum} items`;else if(e.code===o.ZodIssueCode.too_small)s="minItems",a=`${d}/minItems`,n={limit:e.minimum},r=`must NOT have fewer than ${e.minimum} items`;else if(e.code===o.ZodIssueCode.custom){const{value:s,format:o}=null!==(t=e.params)&&void 0!==t?t:{};r=void 0===s?`value at "/${a}" is required`:`value "${"object"==typeof s?JSON.stringify(s):s}" at "/${a}" must pass "${o}" validation`,n={value:s}}return{keyword:null!=s?s:i,instancePath:d?`/${d}`:"",schemaPath:a?`#${a}`:"#",params:null!=n?n:{value:e.message},message:null!=r?r:e.message}}))}}t.Validator=c},4894:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Web3Validator=void 0;const s=r(5191),n=r(1666),o=r(7732);t.Web3Validator=class{constructor(){this._validator=s.Validator.factory()}validateJSONSchema(e,t,r){return this._validator.validate(e,t,r)}validate(e,t,r={silent:!1}){var s,a;const i=(0,n.ethAbiToJsonSchema)(e);if(!Array.isArray(i.items)||0!==(null===(s=i.items)||void 0===s?void 0:s.length)||0!==t.length){if(Array.isArray(i.items)&&0===(null===(a=i.items)||void 0===a?void 0:a.length)&&0!==t.length)throw new o.Web3ValidatorError([{instancePath:"/0",schemaPath:"/",keyword:"required",message:"empty schema against data can not be validated",params:t}]);return this._validator.validate(i,t,r)}}}},5809:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ZodError=t.quotelessJson=t.ZodIssueCode=void 0;const s=r(3133);t.ZodIssueCode=s.util.arrayToEnum(["invalid_type","invalid_literal","custom","invalid_union","invalid_union_discriminator","invalid_enum_value","unrecognized_keys","invalid_arguments","invalid_return_type","invalid_date","invalid_string","too_small","too_big","invalid_intersection_types","not_multiple_of","not_finite"]),t.quotelessJson=e=>JSON.stringify(e,null,2).replace(/"([^"]+)":/g,"$1:");class n extends Error{constructor(e){super(),this.issues=[],this.addIssue=e=>{this.issues=[...this.issues,e]},this.addIssues=(e=[])=>{this.issues=[...this.issues,...e]};const t=new.target.prototype;Object.setPrototypeOf?Object.setPrototypeOf(this,t):this.__proto__=t,this.name="ZodError",this.issues=e}get errors(){return this.issues}format(e){const t=e||function(e){return e.message},r={_errors:[]},s=e=>{for(const n of e.issues)if("invalid_union"===n.code)n.unionErrors.map(s);else if("invalid_return_type"===n.code)s(n.returnTypeError);else if("invalid_arguments"===n.code)s(n.argumentsError);else if(0===n.path.length)r._errors.push(t(n));else{let e=r,s=0;for(;s<n.path.length;){const r=n.path[s];s===n.path.length-1?(e[r]=e[r]||{_errors:[]},e[r]._errors.push(t(n))):e[r]=e[r]||{_errors:[]},e=e[r],s++}}};return s(this),r}toString(){return this.message}get message(){return JSON.stringify(this.issues,s.util.jsonStringifyReplacer,2)}get isEmpty(){return 0===this.issues.length}flatten(e=(e=>e.message)){const t={},r=[];for(const s of this.issues)s.path.length>0?(t[s.path[0]]=t[s.path[0]]||[],t[s.path[0]].push(e(s))):r.push(e(s));return{formErrors:r,fieldErrors:t}}get formErrors(){return this.flatten()}}t.ZodError=n,n.create=e=>new n(e)},1909:function(e,t,r){var s=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.getErrorMap=t.setErrorMap=t.defaultErrorMap=void 0;const n=s(r(6013));t.defaultErrorMap=n.default;let o=n.default;t.setErrorMap=function(e){o=e},t.getErrorMap=function(){return o}},4474:function(e,t,r){var s=this&&this.__createBinding||(Object.create?function(e,t,r,s){void 0===s&&(s=r),Object.defineProperty(e,s,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,s){void 0===s&&(s=r),e[s]=t[r]}),n=this&&this.__exportStar||function(e,t){for(var r in e)"default"===r||Object.prototype.hasOwnProperty.call(t,r)||s(t,e,r)};Object.defineProperty(t,"__esModule",{value:!0}),n(r(1909),t),n(r(4735),t),n(r(1832),t),n(r(3133),t),n(r(1176),t),n(r(5809),t)},3682:(e,t)=>{var r;Object.defineProperty(t,"__esModule",{value:!0}),t.errorUtil=void 0,(r=t.errorUtil||(t.errorUtil={})).errToObj=e=>"string"==typeof e?{message:e}:e||{},r.toString=e=>"string"==typeof e?e:null==e?void 0:e.message},4735:function(e,t,r){var s=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.isAsync=t.isValid=t.isDirty=t.isAborted=t.OK=t.DIRTY=t.INVALID=t.ParseStatus=t.addIssueToContext=t.EMPTY_PATH=t.makeIssue=void 0;const n=r(1909),o=s(r(6013));t.makeIssue=e=>{const{data:t,path:r,errorMaps:s,issueData:n}=e,o=[...r,...n.path||[]],a={...n,path:o};let i="";const d=s.filter((e=>!!e)).slice().reverse();for(const e of d)i=e(a,{data:t,defaultError:i}).message;return{...n,path:o,message:n.message||i}},t.EMPTY_PATH=[],t.addIssueToContext=function(e,r){const s=(0,t.makeIssue)({issueData:r,data:e.data,path:e.path,errorMaps:[e.common.contextualErrorMap,e.schemaErrorMap,(0,n.getErrorMap)(),o.default].filter((e=>!!e))});e.common.issues.push(s)};class a{constructor(){this.value="valid"}dirty(){"valid"===this.value&&(this.value="dirty")}abort(){"aborted"!==this.value&&(this.value="aborted")}static mergeArray(e,r){const s=[];for(const n of r){if("aborted"===n.status)return t.INVALID;"dirty"===n.status&&e.dirty(),s.push(n.value)}return{status:e.value,value:s}}static async mergeObjectAsync(e,t){const r=[];for(const e of t)r.push({key:await e.key,value:await e.value});return a.mergeObjectSync(e,r)}static mergeObjectSync(e,r){const s={};for(const n of r){const{key:r,value:o}=n;if("aborted"===r.status)return t.INVALID;if("aborted"===o.status)return t.INVALID;"dirty"===r.status&&e.dirty(),"dirty"===o.status&&e.dirty(),"__proto__"===r.value||void 0===o.value&&!n.alwaysSet||(s[r.value]=o.value)}return{status:e.value,value:s}}}t.ParseStatus=a,t.INVALID=Object.freeze({status:"aborted"}),t.DIRTY=e=>({status:"dirty",value:e}),t.OK=e=>({status:"valid",value:e}),t.isAborted=e=>"aborted"===e.status,t.isDirty=e=>"dirty"===e.status,t.isValid=e=>"valid"===e.status,t.isAsync=e=>"undefined"!=typeof Promise&&e instanceof Promise},1832:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0})},3133:(e,t)=>{var r;Object.defineProperty(t,"__esModule",{value:!0}),t.getParsedType=t.ZodParsedType=t.objectUtil=t.util=void 0,function(e){e.assertEqual=e=>e,e.assertIs=function(e){},e.assertNever=function(e){throw new Error},e.arrayToEnum=e=>{const t={};for(const r of e)t[r]=r;return t},e.getValidEnumValues=t=>{const r=e.objectKeys(t).filter((e=>"number"!=typeof t[t[e]])),s={};for(const e of r)s[e]=t[e];return e.objectValues(s)},e.objectValues=t=>e.objectKeys(t).map((function(e){return t[e]})),e.objectKeys="function"==typeof Object.keys?e=>Object.keys(e):e=>{const t=[];for(const r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.push(r);return t},e.find=(e,t)=>{for(const r of e)if(t(r))return r},e.isInteger="function"==typeof Number.isInteger?e=>Number.isInteger(e):e=>"number"==typeof e&&isFinite(e)&&Math.floor(e)===e,e.joinValues=function(e,t=" | "){return e.map((e=>"string"==typeof e?`'${e}'`:e)).join(t)},e.jsonStringifyReplacer=(e,t)=>"bigint"==typeof t?t.toString():t}(r=t.util||(t.util={})),(t.objectUtil||(t.objectUtil={})).mergeShapes=(e,t)=>({...e,...t}),t.ZodParsedType=r.arrayToEnum(["string","nan","number","integer","float","boolean","date","bigint","symbol","function","undefined","null","array","object","unknown","promise","void","never","map","set"]),t.getParsedType=e=>{switch(typeof e){case"undefined":return t.ZodParsedType.undefined;case"string":return t.ZodParsedType.string;case"number":return isNaN(e)?t.ZodParsedType.nan:t.ZodParsedType.number;case"boolean":return t.ZodParsedType.boolean;case"function":return t.ZodParsedType.function;case"bigint":return t.ZodParsedType.bigint;case"symbol":return t.ZodParsedType.symbol;case"object":return Array.isArray(e)?t.ZodParsedType.array:null===e?t.ZodParsedType.null:e.then&&"function"==typeof e.then&&e.catch&&"function"==typeof e.catch?t.ZodParsedType.promise:"undefined"!=typeof Map&&e instanceof Map?t.ZodParsedType.map:"undefined"!=typeof Set&&e instanceof Set?t.ZodParsedType.set:"undefined"!=typeof Date&&e instanceof Date?t.ZodParsedType.date:t.ZodParsedType.object;default:return t.ZodParsedType.unknown}}},6750:function(e,t,r){var s=this&&this.__createBinding||(Object.create?function(e,t,r,s){void 0===s&&(s=r),Object.defineProperty(e,s,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,s){void 0===s&&(s=r),e[s]=t[r]}),n=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)"default"!==r&&Object.prototype.hasOwnProperty.call(e,r)&&s(t,e,r);return n(t,e),t},a=this&&this.__exportStar||function(e,t){for(var r in e)"default"===r||Object.prototype.hasOwnProperty.call(t,r)||s(t,e,r)};Object.defineProperty(t,"__esModule",{value:!0}),t.z=void 0;const i=o(r(4474));t.z=i,a(r(4474),t),t.default=i},6013:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});const s=r(3133),n=r(5809);t.default=(e,t)=>{let r;switch(e.code){case n.ZodIssueCode.invalid_type:r=e.received===s.ZodParsedType.undefined?"Required":`Expected ${e.expected}, received ${e.received}`;break;case n.ZodIssueCode.invalid_literal:r=`Invalid literal value, expected ${JSON.stringify(e.expected,s.util.jsonStringifyReplacer)}`;break;case n.ZodIssueCode.unrecognized_keys:r=`Unrecognized key(s) in object: ${s.util.joinValues(e.keys,", ")}`;break;case n.ZodIssueCode.invalid_union:r="Invalid input";break;case n.ZodIssueCode.invalid_union_discriminator:r=`Invalid discriminator value. Expected ${s.util.joinValues(e.options)}`;break;case n.ZodIssueCode.invalid_enum_value:r=`Invalid enum value. Expected ${s.util.joinValues(e.options)}, received '${e.received}'`;break;case n.ZodIssueCode.invalid_arguments:r="Invalid function arguments";break;case n.ZodIssueCode.invalid_return_type:r="Invalid function return type";break;case n.ZodIssueCode.invalid_date:r="Invalid date";break;case n.ZodIssueCode.invalid_string:"object"==typeof e.validation?"includes"in e.validation?(r=`Invalid input: must include "${e.validation.includes}"`,"number"==typeof e.validation.position&&(r=`${r} at one or more positions greater than or equal to ${e.validation.position}`)):"startsWith"in e.validation?r=`Invalid input: must start with "${e.validation.startsWith}"`:"endsWith"in e.validation?r=`Invalid input: must end with "${e.validation.endsWith}"`:s.util.assertNever(e.validation):r="regex"!==e.validation?`Invalid ${e.validation}`:"Invalid";break;case n.ZodIssueCode.too_small:r="array"===e.type?`Array must contain ${e.exact?"exactly":e.inclusive?"at least":"more than"} ${e.minimum} element(s)`:"string"===e.type?`String must contain ${e.exact?"exactly":e.inclusive?"at least":"over"} ${e.minimum} character(s)`:"number"===e.type?`Number must be ${e.exact?"exactly equal to ":e.inclusive?"greater than or equal to ":"greater than "}${e.minimum}`:"date"===e.type?`Date must be ${e.exact?"exactly equal to ":e.inclusive?"greater than or equal to ":"greater than "}${new Date(Number(e.minimum))}`:"Invalid input";break;case n.ZodIssueCode.too_big:r="array"===e.type?`Array must contain ${e.exact?"exactly":e.inclusive?"at most":"less than"} ${e.maximum} element(s)`:"string"===e.type?`String must contain ${e.exact?"exactly":e.inclusive?"at most":"under"} ${e.maximum} character(s)`:"number"===e.type?`Number must be ${e.exact?"exactly":e.inclusive?"less than or equal to":"less than"} ${e.maximum}`:"bigint"===e.type?`BigInt must be ${e.exact?"exactly":e.inclusive?"less than or equal to":"less than"} ${e.maximum}`:"date"===e.type?`Date must be ${e.exact?"exactly":e.inclusive?"smaller than or equal to":"smaller than"} ${new Date(Number(e.maximum))}`:"Invalid input";break;case n.ZodIssueCode.custom:r="Invalid input";break;case n.ZodIssueCode.invalid_intersection_types:r="Intersection results could not be merged";break;case n.ZodIssueCode.not_multiple_of:r=`Number must be a multiple of ${e.multipleOf}`;break;case n.ZodIssueCode.not_finite:r="Number must be finite";break;default:r=t.defaultError,s.util.assertNever(e)}return{message:r}}},1176:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.date=t.boolean=t.bigint=t.array=t.any=t.coerce=t.ZodFirstPartyTypeKind=t.late=t.ZodSchema=t.Schema=t.custom=t.ZodReadonly=t.ZodPipeline=t.ZodBranded=t.BRAND=t.ZodNaN=t.ZodCatch=t.ZodDefault=t.ZodNullable=t.ZodOptional=t.ZodTransformer=t.ZodEffects=t.ZodPromise=t.ZodNativeEnum=t.ZodEnum=t.ZodLiteral=t.ZodLazy=t.ZodFunction=t.ZodSet=t.ZodMap=t.ZodRecord=t.ZodTuple=t.ZodIntersection=t.ZodDiscriminatedUnion=t.ZodUnion=t.ZodObject=t.ZodArray=t.ZodVoid=t.ZodNever=t.ZodUnknown=t.ZodAny=t.ZodNull=t.ZodUndefined=t.ZodSymbol=t.ZodDate=t.ZodBoolean=t.ZodBigInt=t.ZodNumber=t.ZodString=t.ZodType=void 0,t.NEVER=t.void=t.unknown=t.union=t.undefined=t.tuple=t.transformer=t.symbol=t.string=t.strictObject=t.set=t.record=t.promise=t.preprocess=t.pipeline=t.ostring=t.optional=t.onumber=t.oboolean=t.object=t.number=t.nullable=t.null=t.never=t.nativeEnum=t.nan=t.map=t.literal=t.lazy=t.intersection=t.instanceof=t.function=t.enum=t.effect=t.discriminatedUnion=void 0;const s=r(1909),n=r(3682),o=r(4735),a=r(3133),i=r(5809);class d{constructor(e,t,r,s){this._cachedPath=[],this.parent=e,this.data=t,this._path=r,this._key=s}get path(){return this._cachedPath.length||(this._key instanceof Array?this._cachedPath.push(...this._path,...this._key):this._cachedPath.push(...this._path,this._key)),this._cachedPath}}const c=(e,t)=>{if((0,o.isValid)(t))return{success:!0,data:t.value};if(!e.common.issues.length)throw new Error("Validation failed but no issues detected.");return{success:!1,get error(){if(this._error)return this._error;const t=new i.ZodError(e.common.issues);return this._error=t,this._error}}};function u(e){if(!e)return{};const{errorMap:t,invalid_type_error:r,required_error:s,description:n}=e;if(t&&(r||s))throw new Error('Can\'t use "invalid_type_error" or "required_error" in conjunction with custom error map.');return t?{errorMap:t,description:n}:{errorMap:(e,t)=>"invalid_type"!==e.code?{message:t.defaultError}:void 0===t.data?{message:null!=s?s:t.defaultError}:{message:null!=r?r:t.defaultError},description:n}}class l{constructor(e){this.spa=this.safeParseAsync,this._def=e,this.parse=this.parse.bind(this),this.safeParse=this.safeParse.bind(this),this.parseAsync=this.parseAsync.bind(this),this.safeParseAsync=this.safeParseAsync.bind(this),this.spa=this.spa.bind(this),this.refine=this.refine.bind(this),this.refinement=this.refinement.bind(this),this.superRefine=this.superRefine.bind(this),this.optional=this.optional.bind(this),this.nullable=this.nullable.bind(this),this.nullish=this.nullish.bind(this),this.array=this.array.bind(this),this.promise=this.promise.bind(this),this.or=this.or.bind(this),this.and=this.and.bind(this),this.transform=this.transform.bind(this),this.brand=this.brand.bind(this),this.default=this.default.bind(this),this.catch=this.catch.bind(this),this.describe=this.describe.bind(this),this.pipe=this.pipe.bind(this),this.readonly=this.readonly.bind(this),this.isNullable=this.isNullable.bind(this),this.isOptional=this.isOptional.bind(this)}get description(){return this._def.description}_getType(e){return(0,a.getParsedType)(e.data)}_getOrReturnCtx(e,t){return t||{common:e.parent.common,data:e.data,parsedType:(0,a.getParsedType)(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}_processInputParams(e){return{status:new o.ParseStatus,ctx:{common:e.parent.common,data:e.data,parsedType:(0,a.getParsedType)(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}}_parseSync(e){const t=this._parse(e);if((0,o.isAsync)(t))throw new Error("Synchronous parse encountered promise.");return t}_parseAsync(e){const t=this._parse(e);return Promise.resolve(t)}parse(e,t){const r=this.safeParse(e,t);if(r.success)return r.data;throw r.error}safeParse(e,t){var r;const s={common:{issues:[],async:null!==(r=null==t?void 0:t.async)&&void 0!==r&&r,contextualErrorMap:null==t?void 0:t.errorMap},path:(null==t?void 0:t.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:(0,a.getParsedType)(e)},n=this._parseSync({data:e,path:s.path,parent:s});return c(s,n)}async parseAsync(e,t){const r=await this.safeParseAsync(e,t);if(r.success)return r.data;throw r.error}async safeParseAsync(e,t){const r={common:{issues:[],contextualErrorMap:null==t?void 0:t.errorMap,async:!0},path:(null==t?void 0:t.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:(0,a.getParsedType)(e)},s=this._parse({data:e,path:r.path,parent:r}),n=await((0,o.isAsync)(s)?s:Promise.resolve(s));return c(r,n)}refine(e,t){const r=e=>"string"==typeof t||void 0===t?{message:t}:"function"==typeof t?t(e):t;return this._refinement(((t,s)=>{const n=e(t),o=()=>s.addIssue({code:i.ZodIssueCode.custom,...r(t)});return"undefined"!=typeof Promise&&n instanceof Promise?n.then((e=>!!e||(o(),!1))):!!n||(o(),!1)}))}refinement(e,t){return this._refinement(((r,s)=>!!e(r)||(s.addIssue("function"==typeof t?t(r,s):t),!1)))}_refinement(e){return new q({schema:this,typeName:ie.ZodEffects,effect:{type:"refinement",refinement:e}})}superRefine(e){return this._refinement(e)}optional(){return Q.create(this,this._def)}nullable(){return ee.create(this,this._def)}nullish(){return this.nullable().optional()}array(){return M.create(this,this._def)}promise(){return Y.create(this,this._def)}or(e){return w.create([this,e],this._def)}and(e){return B.create(this,e,this._def)}transform(e){return new q({...u(this._def),schema:this,typeName:ie.ZodEffects,effect:{type:"transform",transform:e}})}default(e){const t="function"==typeof e?e:()=>e;return new te({...u(this._def),innerType:this,defaultValue:t,typeName:ie.ZodDefault})}brand(){return new ne({typeName:ie.ZodBranded,type:this,...u(this._def)})}catch(e){const t="function"==typeof e?e:()=>e;return new re({...u(this._def),innerType:this,catchValue:t,typeName:ie.ZodCatch})}describe(e){return new(0,this.constructor)({...this._def,description:e})}pipe(e){return oe.create(this,e)}readonly(){return ae.create(this)}isOptional(){return this.safeParse(void 0).success}isNullable(){return this.safeParse(null).success}}t.ZodType=l,t.Schema=l,t.ZodSchema=l;const _=/^c[^\s-]{8,}$/i,p=/^[a-z][a-z0-9]*$/,h=/[0-9A-HJKMNP-TV-Z]{26}/,E=/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i,R=/^(?!\.)(?!.*\.\.)([A-Z0-9_+-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i,f=/^(\p{Extended_Pictographic}|\p{Emoji_Component})+$/u,m=/^(((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2}))\.){3}((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2}))$/,I=/^(([a-f0-9]{1,4}:){7}|::([a-f0-9]{1,4}:){0,6}|([a-f0-9]{1,4}:){1}:([a-f0-9]{1,4}:){0,5}|([a-f0-9]{1,4}:){2}:([a-f0-9]{1,4}:){0,4}|([a-f0-9]{1,4}:){3}:([a-f0-9]{1,4}:){0,3}|([a-f0-9]{1,4}:){4}:([a-f0-9]{1,4}:){0,2}|([a-f0-9]{1,4}:){5}:([a-f0-9]{1,4}:){0,1})([a-f0-9]{1,4}|(((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2}))\.){3}((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2})))$/;class y extends l{constructor(){super(...arguments),this._regex=(e,t,r)=>this.refinement((t=>e.test(t)),{validation:t,code:i.ZodIssueCode.invalid_string,...n.errorUtil.errToObj(r)}),this.nonempty=e=>this.min(1,n.errorUtil.errToObj(e)),this.trim=()=>new y({...this._def,checks:[...this._def.checks,{kind:"trim"}]}),this.toLowerCase=()=>new y({...this._def,checks:[...this._def.checks,{kind:"toLowerCase"}]}),this.toUpperCase=()=>new y({...this._def,checks:[...this._def.checks,{kind:"toUpperCase"}]})}_parse(e){if(this._def.coerce&&(e.data=String(e.data)),this._getType(e)!==a.ZodParsedType.string){const t=this._getOrReturnCtx(e);return(0,o.addIssueToContext)(t,{code:i.ZodIssueCode.invalid_type,expected:a.ZodParsedType.string,received:t.parsedType}),o.INVALID}const t=new o.ParseStatus;let r;for(const c of this._def.checks)if("min"===c.kind)e.data.length<c.value&&(r=this._getOrReturnCtx(e,r),(0,o.addIssueToContext)(r,{code:i.ZodIssueCode.too_small,minimum:c.value,type:"string",inclusive:!0,exact:!1,message:c.message}),t.dirty());else if("max"===c.kind)e.data.length>c.value&&(r=this._getOrReturnCtx(e,r),(0,o.addIssueToContext)(r,{code:i.ZodIssueCode.too_big,maximum:c.value,type:"string",inclusive:!0,exact:!1,message:c.message}),t.dirty());else if("length"===c.kind){const s=e.data.length>c.value,n=e.data.length<c.value;(s||n)&&(r=this._getOrReturnCtx(e,r),s?(0,o.addIssueToContext)(r,{code:i.ZodIssueCode.too_big,maximum:c.value,type:"string",inclusive:!0,exact:!0,message:c.message}):n&&(0,o.addIssueToContext)(r,{code:i.ZodIssueCode.too_small,minimum:c.value,type:"string",inclusive:!0,exact:!0,message:c.message}),t.dirty())}else if("email"===c.kind)R.test(e.data)||(r=this._getOrReturnCtx(e,r),(0,o.addIssueToContext)(r,{validation:"email",code:i.ZodIssueCode.invalid_string,message:c.message}),t.dirty());else if("emoji"===c.kind)f.test(e.data)||(r=this._getOrReturnCtx(e,r),(0,o.addIssueToContext)(r,{validation:"emoji",code:i.ZodIssueCode.invalid_string,message:c.message}),t.dirty());else if("uuid"===c.kind)E.test(e.data)||(r=this._getOrReturnCtx(e,r),(0,o.addIssueToContext)(r,{validation:"uuid",code:i.ZodIssueCode.invalid_string,message:c.message}),t.dirty());else if("cuid"===c.kind)_.test(e.data)||(r=this._getOrReturnCtx(e,r),(0,o.addIssueToContext)(r,{validation:"cuid",code:i.ZodIssueCode.invalid_string,message:c.message}),t.dirty());else if("cuid2"===c.kind)p.test(e.data)||(r=this._getOrReturnCtx(e,r),(0,o.addIssueToContext)(r,{validation:"cuid2",code:i.ZodIssueCode.invalid_string,message:c.message}),t.dirty());else if("ulid"===c.kind)h.test(e.data)||(r=this._getOrReturnCtx(e,r),(0,o.addIssueToContext)(r,{validation:"ulid",code:i.ZodIssueCode.invalid_string,message:c.message}),t.dirty());else if("url"===c.kind)try{new URL(e.data)}catch(s){r=this._getOrReturnCtx(e,r),(0,o.addIssueToContext)(r,{validation:"url",code:i.ZodIssueCode.invalid_string,message:c.message}),t.dirty()}else"regex"===c.kind?(c.regex.lastIndex=0,c.regex.test(e.data)||(r=this._getOrReturnCtx(e,r),(0,o.addIssueToContext)(r,{validation:"regex",code:i.ZodIssueCode.invalid_string,message:c.message}),t.dirty())):"trim"===c.kind?e.data=e.data.trim():"includes"===c.kind?e.data.includes(c.value,c.position)||(r=this._getOrReturnCtx(e,r),(0,o.addIssueToContext)(r,{code:i.ZodIssueCode.invalid_string,validation:{includes:c.value,position:c.position},message:c.message}),t.dirty()):"toLowerCase"===c.kind?e.data=e.data.toLowerCase():"toUpperCase"===c.kind?e.data=e.data.toUpperCase():"startsWith"===c.kind?e.data.startsWith(c.value)||(r=this._getOrReturnCtx(e,r),(0,o.addIssueToContext)(r,{code:i.ZodIssueCode.invalid_string,validation:{startsWith:c.value},message:c.message}),t.dirty()):"endsWith"===c.kind?e.data.endsWith(c.value)||(r=this._getOrReturnCtx(e,r),(0,o.addIssueToContext)(r,{code:i.ZodIssueCode.invalid_string,validation:{endsWith:c.value},message:c.message}),t.dirty()):"datetime"===c.kind?((d=c).precision?d.offset?new RegExp(`^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}\\.\\d{${d.precision}}(([+-]\\d{2}(:?\\d{2})?)|Z)$`):new RegExp(`^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}\\.\\d{${d.precision}}Z$`):0===d.precision?d.offset?new RegExp("^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}(([+-]\\d{2}(:?\\d{2})?)|Z)$"):new RegExp("^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}Z$"):d.offset?new RegExp("^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}(\\.\\d+)?(([+-]\\d{2}(:?\\d{2})?)|Z)$"):new RegExp("^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}(\\.\\d+)?Z$")).test(e.data)||(r=this._getOrReturnCtx(e,r),(0,o.addIssueToContext)(r,{code:i.ZodIssueCode.invalid_string,validation:"datetime",message:c.message}),t.dirty()):"ip"===c.kind?(s=e.data,("v4"!==(n=c.version)&&n||!m.test(s))&&("v6"!==n&&n||!I.test(s))&&(r=this._getOrReturnCtx(e,r),(0,o.addIssueToContext)(r,{validation:"ip",code:i.ZodIssueCode.invalid_string,message:c.message}),t.dirty())):a.util.assertNever(c);var s,n,d;return{status:t.value,value:e.data}}_addCheck(e){return new y({...this._def,checks:[...this._def.checks,e]})}email(e){return this._addCheck({kind:"email",...n.errorUtil.errToObj(e)})}url(e){return this._addCheck({kind:"url",...n.errorUtil.errToObj(e)})}emoji(e){return this._addCheck({kind:"emoji",...n.errorUtil.errToObj(e)})}uuid(e){return this._addCheck({kind:"uuid",...n.errorUtil.errToObj(e)})}cuid(e){return this._addCheck({kind:"cuid",...n.errorUtil.errToObj(e)})}cuid2(e){return this._addCheck({kind:"cuid2",...n.errorUtil.errToObj(e)})}ulid(e){return this._addCheck({kind:"ulid",...n.errorUtil.errToObj(e)})}ip(e){return this._addCheck({kind:"ip",...n.errorUtil.errToObj(e)})}datetime(e){var t;return"string"==typeof e?this._addCheck({kind:"datetime",precision:null,offset:!1,message:e}):this._addCheck({kind:"datetime",precision:void 0===(null==e?void 0:e.precision)?null:null==e?void 0:e.precision,offset:null!==(t=null==e?void 0:e.offset)&&void 0!==t&&t,...n.errorUtil.errToObj(null==e?void 0:e.message)})}regex(e,t){return this._addCheck({kind:"regex",regex:e,...n.errorUtil.errToObj(t)})}includes(e,t){return this._addCheck({kind:"includes",value:e,position:null==t?void 0:t.position,...n.errorUtil.errToObj(null==t?void 0:t.message)})}startsWith(e,t){return this._addCheck({kind:"startsWith",value:e,...n.errorUtil.errToObj(t)})}endsWith(e,t){return this._addCheck({kind:"endsWith",value:e,...n.errorUtil.errToObj(t)})}min(e,t){return this._addCheck({kind:"min",value:e,...n.errorUtil.errToObj(t)})}max(e,t){return this._addCheck({kind:"max",value:e,...n.errorUtil.errToObj(t)})}length(e,t){return this._addCheck({kind:"length",value:e,...n.errorUtil.errToObj(t)})}get isDatetime(){return!!this._def.checks.find((e=>"datetime"===e.kind))}get isEmail(){return!!this._def.checks.find((e=>"email"===e.kind))}get isURL(){return!!this._def.checks.find((e=>"url"===e.kind))}get isEmoji(){return!!this._def.checks.find((e=>"emoji"===e.kind))}get isUUID(){return!!this._def.checks.find((e=>"uuid"===e.kind))}get isCUID(){return!!this._def.checks.find((e=>"cuid"===e.kind))}get isCUID2(){return!!this._def.checks.find((e=>"cuid2"===e.kind))}get isULID(){return!!this._def.checks.find((e=>"ulid"===e.kind))}get isIP(){return!!this._def.checks.find((e=>"ip"===e.kind))}get minLength(){let e=null;for(const t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxLength(){let e=null;for(const t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}}function T(e,t){const r=(e.toString().split(".")[1]||"").length,s=(t.toString().split(".")[1]||"").length,n=r>s?r:s;return parseInt(e.toFixed(n).replace(".",""))%parseInt(t.toFixed(n).replace(".",""))/Math.pow(10,n)}t.ZodString=y,y.create=e=>{var t;return new y({checks:[],typeName:ie.ZodString,coerce:null!==(t=null==e?void 0:e.coerce)&&void 0!==t&&t,...u(e)})};class v extends l{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte,this.step=this.multipleOf}_parse(e){if(this._def.coerce&&(e.data=Number(e.data)),this._getType(e)!==a.ZodParsedType.number){const t=this._getOrReturnCtx(e);return(0,o.addIssueToContext)(t,{code:i.ZodIssueCode.invalid_type,expected:a.ZodParsedType.number,received:t.parsedType}),o.INVALID}let t;const r=new o.ParseStatus;for(const s of this._def.checks)"int"===s.kind?a.util.isInteger(e.data)||(t=this._getOrReturnCtx(e,t),(0,o.addIssueToContext)(t,{code:i.ZodIssueCode.invalid_type,expected:"integer",received:"float",message:s.message}),r.dirty()):"min"===s.kind?(s.inclusive?e.data<s.value:e.data<=s.value)&&(t=this._getOrReturnCtx(e,t),(0,o.addIssueToContext)(t,{code:i.ZodIssueCode.too_small,minimum:s.value,type:"number",inclusive:s.inclusive,exact:!1,message:s.message}),r.dirty()):"max"===s.kind?(s.inclusive?e.data>s.value:e.data>=s.value)&&(t=this._getOrReturnCtx(e,t),(0,o.addIssueToContext)(t,{code:i.ZodIssueCode.too_big,maximum:s.value,type:"number",inclusive:s.inclusive,exact:!1,message:s.message}),r.dirty()):"multipleOf"===s.kind?0!==T(e.data,s.value)&&(t=this._getOrReturnCtx(e,t),(0,o.addIssueToContext)(t,{code:i.ZodIssueCode.not_multiple_of,multipleOf:s.value,message:s.message}),r.dirty()):"finite"===s.kind?Number.isFinite(e.data)||(t=this._getOrReturnCtx(e,t),(0,o.addIssueToContext)(t,{code:i.ZodIssueCode.not_finite,message:s.message}),r.dirty()):a.util.assertNever(s);return{status:r.value,value:e.data}}gte(e,t){return this.setLimit("min",e,!0,n.errorUtil.toString(t))}gt(e,t){return this.setLimit("min",e,!1,n.errorUtil.toString(t))}lte(e,t){return this.setLimit("max",e,!0,n.errorUtil.toString(t))}lt(e,t){return this.setLimit("max",e,!1,n.errorUtil.toString(t))}setLimit(e,t,r,s){return new v({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:r,message:n.errorUtil.toString(s)}]})}_addCheck(e){return new v({...this._def,checks:[...this._def.checks,e]})}int(e){return this._addCheck({kind:"int",message:n.errorUtil.toString(e)})}positive(e){return this._addCheck({kind:"min",value:0,inclusive:!1,message:n.errorUtil.toString(e)})}negative(e){return this._addCheck({kind:"max",value:0,inclusive:!1,message:n.errorUtil.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:0,inclusive:!0,message:n.errorUtil.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:0,inclusive:!0,message:n.errorUtil.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:n.errorUtil.toString(t)})}finite(e){return this._addCheck({kind:"finite",message:n.errorUtil.toString(e)})}safe(e){return this._addCheck({kind:"min",inclusive:!0,value:Number.MIN_SAFE_INTEGER,message:n.errorUtil.toString(e)})._addCheck({kind:"max",inclusive:!0,value:Number.MAX_SAFE_INTEGER,message:n.errorUtil.toString(e)})}get minValue(){let e=null;for(const t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(const t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}get isInt(){return!!this._def.checks.find((e=>"int"===e.kind||"multipleOf"===e.kind&&a.util.isInteger(e.value)))}get isFinite(){let e=null,t=null;for(const r of this._def.checks){if("finite"===r.kind||"int"===r.kind||"multipleOf"===r.kind)return!0;"min"===r.kind?(null===t||r.value>t)&&(t=r.value):"max"===r.kind&&(null===e||r.value<e)&&(e=r.value)}return Number.isFinite(t)&&Number.isFinite(e)}}t.ZodNumber=v,v.create=e=>new v({checks:[],typeName:ie.ZodNumber,coerce:(null==e?void 0:e.coerce)||!1,...u(e)});class g extends l{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte}_parse(e){if(this._def.coerce&&(e.data=BigInt(e.data)),this._getType(e)!==a.ZodParsedType.bigint){const t=this._getOrReturnCtx(e);return(0,o.addIssueToContext)(t,{code:i.ZodIssueCode.invalid_type,expected:a.ZodParsedType.bigint,received:t.parsedType}),o.INVALID}let t;const r=new o.ParseStatus;for(const s of this._def.checks)"min"===s.kind?(s.inclusive?e.data<s.value:e.data<=s.value)&&(t=this._getOrReturnCtx(e,t),(0,o.addIssueToContext)(t,{code:i.ZodIssueCode.too_small,type:"bigint",minimum:s.value,inclusive:s.inclusive,message:s.message}),r.dirty()):"max"===s.kind?(s.inclusive?e.data>s.value:e.data>=s.value)&&(t=this._getOrReturnCtx(e,t),(0,o.addIssueToContext)(t,{code:i.ZodIssueCode.too_big,type:"bigint",maximum:s.value,inclusive:s.inclusive,message:s.message}),r.dirty()):"multipleOf"===s.kind?e.data%s.value!==BigInt(0)&&(t=this._getOrReturnCtx(e,t),(0,o.addIssueToContext)(t,{code:i.ZodIssueCode.not_multiple_of,multipleOf:s.value,message:s.message}),r.dirty()):a.util.assertNever(s);return{status:r.value,value:e.data}}gte(e,t){return this.setLimit("min",e,!0,n.errorUtil.toString(t))}gt(e,t){return this.setLimit("min",e,!1,n.errorUtil.toString(t))}lte(e,t){return this.setLimit("max",e,!0,n.errorUtil.toString(t))}lt(e,t){return this.setLimit("max",e,!1,n.errorUtil.toString(t))}setLimit(e,t,r,s){return new g({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:r,message:n.errorUtil.toString(s)}]})}_addCheck(e){return new g({...this._def,checks:[...this._def.checks,e]})}positive(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!1,message:n.errorUtil.toString(e)})}negative(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!1,message:n.errorUtil.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!0,message:n.errorUtil.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!0,message:n.errorUtil.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:n.errorUtil.toString(t)})}get minValue(){let e=null;for(const t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(const t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}}t.ZodBigInt=g,g.create=e=>{var t;return new g({checks:[],typeName:ie.ZodBigInt,coerce:null!==(t=null==e?void 0:e.coerce)&&void 0!==t&&t,...u(e)})};class N extends l{_parse(e){if(this._def.coerce&&(e.data=Boolean(e.data)),this._getType(e)!==a.ZodParsedType.boolean){const t=this._getOrReturnCtx(e);return(0,o.addIssueToContext)(t,{code:i.ZodIssueCode.invalid_type,expected:a.ZodParsedType.boolean,received:t.parsedType}),o.INVALID}return(0,o.OK)(e.data)}}t.ZodBoolean=N,N.create=e=>new N({typeName:ie.ZodBoolean,coerce:(null==e?void 0:e.coerce)||!1,...u(e)});class b extends l{_parse(e){if(this._def.coerce&&(e.data=new Date(e.data)),this._getType(e)!==a.ZodParsedType.date){const t=this._getOrReturnCtx(e);return(0,o.addIssueToContext)(t,{code:i.ZodIssueCode.invalid_type,expected:a.ZodParsedType.date,received:t.parsedType}),o.INVALID}if(isNaN(e.data.getTime())){const t=this._getOrReturnCtx(e);return(0,o.addIssueToContext)(t,{code:i.ZodIssueCode.invalid_date}),o.INVALID}const t=new o.ParseStatus;let r;for(const s of this._def.checks)"min"===s.kind?e.data.getTime()<s.value&&(r=this._getOrReturnCtx(e,r),(0,o.addIssueToContext)(r,{code:i.ZodIssueCode.too_small,message:s.message,inclusive:!0,exact:!1,minimum:s.value,type:"date"}),t.dirty()):"max"===s.kind?e.data.getTime()>s.value&&(r=this._getOrReturnCtx(e,r),(0,o.addIssueToContext)(r,{code:i.ZodIssueCode.too_big,message:s.message,inclusive:!0,exact:!1,maximum:s.value,type:"date"}),t.dirty()):a.util.assertNever(s);return{status:t.value,value:new Date(e.data.getTime())}}_addCheck(e){return new b({...this._def,checks:[...this._def.checks,e]})}min(e,t){return this._addCheck({kind:"min",value:e.getTime(),message:n.errorUtil.toString(t)})}max(e,t){return this._addCheck({kind:"max",value:e.getTime(),message:n.errorUtil.toString(t)})}get minDate(){let e=null;for(const t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return null!=e?new Date(e):null}get maxDate(){let e=null;for(const t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return null!=e?new Date(e):null}}t.ZodDate=b,b.create=e=>new b({checks:[],coerce:(null==e?void 0:e.coerce)||!1,typeName:ie.ZodDate,...u(e)});class O extends l{_parse(e){if(this._getType(e)!==a.ZodParsedType.symbol){const t=this._getOrReturnCtx(e);return(0,o.addIssueToContext)(t,{code:i.ZodIssueCode.invalid_type,expected:a.ZodParsedType.symbol,received:t.parsedType}),o.INVALID}return(0,o.OK)(e.data)}}t.ZodSymbol=O,O.create=e=>new O({typeName:ie.ZodSymbol,...u(e)});class A extends l{_parse(e){if(this._getType(e)!==a.ZodParsedType.undefined){const t=this._getOrReturnCtx(e);return(0,o.addIssueToContext)(t,{code:i.ZodIssueCode.invalid_type,expected:a.ZodParsedType.undefined,received:t.parsedType}),o.INVALID}return(0,o.OK)(e.data)}}t.ZodUndefined=A,A.create=e=>new A({typeName:ie.ZodUndefined,...u(e)});class C extends l{_parse(e){if(this._getType(e)!==a.ZodParsedType.null){const t=this._getOrReturnCtx(e);return(0,o.addIssueToContext)(t,{code:i.ZodIssueCode.invalid_type,expected:a.ZodParsedType.null,received:t.parsedType}),o.INVALID}return(0,o.OK)(e.data)}}t.ZodNull=C,C.create=e=>new C({typeName:ie.ZodNull,...u(e)});class S extends l{constructor(){super(...arguments),this._any=!0}_parse(e){return(0,o.OK)(e.data)}}t.ZodAny=S,S.create=e=>new S({typeName:ie.ZodAny,...u(e)});class x extends l{constructor(){super(...arguments),this._unknown=!0}_parse(e){return(0,o.OK)(e.data)}}t.ZodUnknown=x,x.create=e=>new x({typeName:ie.ZodUnknown,...u(e)});class P extends l{_parse(e){const t=this._getOrReturnCtx(e);return(0,o.addIssueToContext)(t,{code:i.ZodIssueCode.invalid_type,expected:a.ZodParsedType.never,received:t.parsedType}),o.INVALID}}t.ZodNever=P,P.create=e=>new P({typeName:ie.ZodNever,...u(e)});class D extends l{_parse(e){if(this._getType(e)!==a.ZodParsedType.undefined){const t=this._getOrReturnCtx(e);return(0,o.addIssueToContext)(t,{code:i.ZodIssueCode.invalid_type,expected:a.ZodParsedType.void,received:t.parsedType}),o.INVALID}return(0,o.OK)(e.data)}}t.ZodVoid=D,D.create=e=>new D({typeName:ie.ZodVoid,...u(e)});class M extends l{_parse(e){const{ctx:t,status:r}=this._processInputParams(e),s=this._def;if(t.parsedType!==a.ZodParsedType.array)return(0,o.addIssueToContext)(t,{code:i.ZodIssueCode.invalid_type,expected:a.ZodParsedType.array,received:t.parsedType}),o.INVALID;if(null!==s.exactLength){const e=t.data.length>s.exactLength.value,n=t.data.length<s.exactLength.value;(e||n)&&((0,o.addIssueToContext)(t,{code:e?i.ZodIssueCode.too_big:i.ZodIssueCode.too_small,minimum:n?s.exactLength.value:void 0,maximum:e?s.exactLength.value:void 0,type:"array",inclusive:!0,exact:!0,message:s.exactLength.message}),r.dirty())}if(null!==s.minLength&&t.data.length<s.minLength.value&&((0,o.addIssueToContext)(t,{code:i.ZodIssueCode.too_small,minimum:s.minLength.value,type:"array",inclusive:!0,exact:!1,message:s.minLength.message}),r.dirty()),null!==s.maxLength&&t.data.length>s.maxLength.value&&((0,o.addIssueToContext)(t,{code:i.ZodIssueCode.too_big,maximum:s.maxLength.value,type:"array",inclusive:!0,exact:!1,message:s.maxLength.message}),r.dirty()),t.common.async)return Promise.all([...t.data].map(((e,r)=>s.type._parseAsync(new d(t,e,t.path,r))))).then((e=>o.ParseStatus.mergeArray(r,e)));const n=[...t.data].map(((e,r)=>s.type._parseSync(new d(t,e,t.path,r))));return o.ParseStatus.mergeArray(r,n)}get element(){return this._def.type}min(e,t){return new M({...this._def,minLength:{value:e,message:n.errorUtil.toString(t)}})}max(e,t){return new M({...this._def,maxLength:{value:e,message:n.errorUtil.toString(t)}})}length(e,t){return new M({...this._def,exactLength:{value:e,message:n.errorUtil.toString(t)}})}nonempty(e){return this.min(1,e)}}function k(e){if(e instanceof L){const t={};for(const r in e.shape){const s=e.shape[r];t[r]=Q.create(k(s))}return new L({...e._def,shape:()=>t})}return e instanceof M?new M({...e._def,type:k(e.element)}):e instanceof Q?Q.create(k(e.unwrap())):e instanceof ee?ee.create(k(e.unwrap())):e instanceof j?j.create(e.items.map((e=>k(e)))):e}t.ZodArray=M,M.create=(e,t)=>new M({type:e,minLength:null,maxLength:null,exactLength:null,typeName:ie.ZodArray,...u(t)});class L extends l{constructor(){super(...arguments),this._cached=null,this.nonstrict=this.passthrough,this.augment=this.extend}_getCached(){if(null!==this._cached)return this._cached;const e=this._def.shape(),t=a.util.objectKeys(e);return this._cached={shape:e,keys:t}}_parse(e){if(this._getType(e)!==a.ZodParsedType.object){const t=this._getOrReturnCtx(e);return(0,o.addIssueToContext)(t,{code:i.ZodIssueCode.invalid_type,expected:a.ZodParsedType.object,received:t.parsedType}),o.INVALID}const{status:t,ctx:r}=this._processInputParams(e),{shape:s,keys:n}=this._getCached(),c=[];if(!(this._def.catchall instanceof P&&"strip"===this._def.unknownKeys))for(const e in r.data)n.includes(e)||c.push(e);const u=[];for(const e of n){const t=s[e],n=r.data[e];u.push({key:{status:"valid",value:e},value:t._parse(new d(r,n,r.path,e)),alwaysSet:e in r.data})}if(this._def.catchall instanceof P){const e=this._def.unknownKeys;if("passthrough"===e)for(const e of c)u.push({key:{status:"valid",value:e},value:{status:"valid",value:r.data[e]}});else if("strict"===e)c.length>0&&((0,o.addIssueToContext)(r,{code:i.ZodIssueCode.unrecognized_keys,keys:c}),t.dirty());else if("strip"!==e)throw new Error("Internal ZodObject error: invalid unknownKeys value.")}else{const e=this._def.catchall;for(const t of c){const s=r.data[t];u.push({key:{status:"valid",value:t},value:e._parse(new d(r,s,r.path,t)),alwaysSet:t in r.data})}}return r.common.async?Promise.resolve().then((async()=>{const e=[];for(const t of u){const r=await t.key;e.push({key:r,value:await t.value,alwaysSet:t.alwaysSet})}return e})).then((e=>o.ParseStatus.mergeObjectSync(t,e))):o.ParseStatus.mergeObjectSync(t,u)}get shape(){return this._def.shape()}strict(e){return n.errorUtil.errToObj,new L({...this._def,unknownKeys:"strict",...void 0!==e?{errorMap:(t,r)=>{var s,o,a,i;const d=null!==(a=null===(o=(s=this._def).errorMap)||void 0===o?void 0:o.call(s,t,r).message)&&void 0!==a?a:r.defaultError;return"unrecognized_keys"===t.code?{message:null!==(i=n.errorUtil.errToObj(e).message)&&void 0!==i?i:d}:{message:d}}}:{}})}strip(){return new L({...this._def,unknownKeys:"strip"})}passthrough(){return new L({...this._def,unknownKeys:"passthrough"})}extend(e){return new L({...this._def,shape:()=>({...this._def.shape(),...e})})}merge(e){return new L({unknownKeys:e._def.unknownKeys,catchall:e._def.catchall,shape:()=>({...this._def.shape(),...e._def.shape()}),typeName:ie.ZodObject})}setKey(e,t){return this.augment({[e]:t})}catchall(e){return new L({...this._def,catchall:e})}pick(e){const t={};return a.util.objectKeys(e).forEach((r=>{e[r]&&this.shape[r]&&(t[r]=this.shape[r])})),new L({...this._def,shape:()=>t})}omit(e){const t={};return a.util.objectKeys(this.shape).forEach((r=>{e[r]||(t[r]=this.shape[r])})),new L({...this._def,shape:()=>t})}deepPartial(){return k(this)}partial(e){const t={};return a.util.objectKeys(this.shape).forEach((r=>{const s=this.shape[r];e&&!e[r]?t[r]=s:t[r]=s.optional()})),new L({...this._def,shape:()=>t})}required(e){const t={};return a.util.objectKeys(this.shape).forEach((r=>{if(e&&!e[r])t[r]=this.shape[r];else{let e=this.shape[r];for(;e instanceof Q;)e=e._def.innerType;t[r]=e}})),new L({...this._def,shape:()=>t})}keyof(){return K(a.util.objectKeys(this.shape))}}t.ZodObject=L,L.create=(e,t)=>new L({shape:()=>e,unknownKeys:"strip",catchall:P.create(),typeName:ie.ZodObject,...u(t)}),L.strictCreate=(e,t)=>new L({shape:()=>e,unknownKeys:"strict",catchall:P.create(),typeName:ie.ZodObject,...u(t)}),L.lazycreate=(e,t)=>new L({shape:e,unknownKeys:"strip",catchall:P.create(),typeName:ie.ZodObject,...u(t)});class w extends l{_parse(e){const{ctx:t}=this._processInputParams(e),r=this._def.options;if(t.common.async)return Promise.all(r.map((async e=>{const r={...t,common:{...t.common,issues:[]},parent:null};return{result:await e._parseAsync({data:t.data,path:t.path,parent:r}),ctx:r}}))).then((function(e){for(const t of e)if("valid"===t.result.status)return t.result;for(const r of e)if("dirty"===r.result.status)return t.common.issues.push(...r.ctx.common.issues),r.result;const r=e.map((e=>new i.ZodError(e.ctx.common.issues)));return(0,o.addIssueToContext)(t,{code:i.ZodIssueCode.invalid_union,unionErrors:r}),o.INVALID}));{let e;const s=[];for(const n of r){const r={...t,common:{...t.common,issues:[]},parent:null},o=n._parseSync({data:t.data,path:t.path,parent:r});if("valid"===o.status)return o;"dirty"!==o.status||e||(e={result:o,ctx:r}),r.common.issues.length&&s.push(r.common.issues)}if(e)return t.common.issues.push(...e.ctx.common.issues),e.result;const n=s.map((e=>new i.ZodError(e)));return(0,o.addIssueToContext)(t,{code:i.ZodIssueCode.invalid_union,unionErrors:n}),o.INVALID}}get options(){return this._def.options}}t.ZodUnion=w,w.create=(e,t)=>new w({options:e,typeName:ie.ZodUnion,...u(t)});const Z=e=>e instanceof X?Z(e.schema):e instanceof q?Z(e.innerType()):e instanceof W?[e.value]:e instanceof J?e.options:e instanceof z?Object.keys(e.enum):e instanceof te?Z(e._def.innerType):e instanceof A?[void 0]:e instanceof C?[null]:null;class U extends l{_parse(e){const{ctx:t}=this._processInputParams(e);if(t.parsedType!==a.ZodParsedType.object)return(0,o.addIssueToContext)(t,{code:i.ZodIssueCode.invalid_type,expected:a.ZodParsedType.object,received:t.parsedType}),o.INVALID;const r=this.discriminator,s=t.data[r],n=this.optionsMap.get(s);return n?t.common.async?n._parseAsync({data:t.data,path:t.path,parent:t}):n._parseSync({data:t.data,path:t.path,parent:t}):((0,o.addIssueToContext)(t,{code:i.ZodIssueCode.invalid_union_discriminator,options:Array.from(this.optionsMap.keys()),path:[r]}),o.INVALID)}get discriminator(){return this._def.discriminator}get options(){return this._def.options}get optionsMap(){return this._def.optionsMap}static create(e,t,r){const s=new Map;for(const r of t){const t=Z(r.shape[e]);if(!t)throw new Error(`A discriminator value for key \`${e}\` could not be extracted from all schema options`);for(const n of t){if(s.has(n))throw new Error(`Discriminator property ${String(e)} has duplicate value ${String(n)}`);s.set(n,r)}}return new U({typeName:ie.ZodDiscriminatedUnion,discriminator:e,options:t,optionsMap:s,...u(r)})}}function V(e,t){const r=(0,a.getParsedType)(e),s=(0,a.getParsedType)(t);if(e===t)return{valid:!0,data:e};if(r===a.ZodParsedType.object&&s===a.ZodParsedType.object){const r=a.util.objectKeys(t),s=a.util.objectKeys(e).filter((e=>-1!==r.indexOf(e))),n={...e,...t};for(const r of s){const s=V(e[r],t[r]);if(!s.valid)return{valid:!1};n[r]=s.data}return{valid:!0,data:n}}if(r===a.ZodParsedType.array&&s===a.ZodParsedType.array){if(e.length!==t.length)return{valid:!1};const r=[];for(let s=0;s<e.length;s++){const n=V(e[s],t[s]);if(!n.valid)return{valid:!1};r.push(n.data)}return{valid:!0,data:r}}return r===a.ZodParsedType.date&&s===a.ZodParsedType.date&&+e==+t?{valid:!0,data:e}:{valid:!1}}t.ZodDiscriminatedUnion=U;class B extends l{_parse(e){const{status:t,ctx:r}=this._processInputParams(e),s=(e,s)=>{if((0,o.isAborted)(e)||(0,o.isAborted)(s))return o.INVALID;const n=V(e.value,s.value);return n.valid?(((0,o.isDirty)(e)||(0,o.isDirty)(s))&&t.dirty(),{status:t.value,value:n.data}):((0,o.addIssueToContext)(r,{code:i.ZodIssueCode.invalid_intersection_types}),o.INVALID)};return r.common.async?Promise.all([this._def.left._parseAsync({data:r.data,path:r.path,parent:r}),this._def.right._parseAsync({data:r.data,path:r.path,parent:r})]).then((([e,t])=>s(e,t))):s(this._def.left._parseSync({data:r.data,path:r.path,parent:r}),this._def.right._parseSync({data:r.data,path:r.path,parent:r}))}}t.ZodIntersection=B,B.create=(e,t,r)=>new B({left:e,right:t,typeName:ie.ZodIntersection,...u(r)});class j extends l{_parse(e){const{status:t,ctx:r}=this._processInputParams(e);if(r.parsedType!==a.ZodParsedType.array)return(0,o.addIssueToContext)(r,{code:i.ZodIssueCode.invalid_type,expected:a.ZodParsedType.array,received:r.parsedType}),o.INVALID;if(r.data.length<this._def.items.length)return(0,o.addIssueToContext)(r,{code:i.ZodIssueCode.too_small,minimum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),o.INVALID;!this._def.rest&&r.data.length>this._def.items.length&&((0,o.addIssueToContext)(r,{code:i.ZodIssueCode.too_big,maximum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),t.dirty());const s=[...r.data].map(((e,t)=>{const s=this._def.items[t]||this._def.rest;return s?s._parse(new d(r,e,r.path,t)):null})).filter((e=>!!e));return r.common.async?Promise.all(s).then((e=>o.ParseStatus.mergeArray(t,e))):o.ParseStatus.mergeArray(t,s)}get items(){return this._def.items}rest(e){return new j({...this._def,rest:e})}}t.ZodTuple=j,j.create=(e,t)=>{if(!Array.isArray(e))throw new Error("You must pass an array of schemas to z.tuple([ ... ])");return new j({items:e,typeName:ie.ZodTuple,rest:null,...u(t)})};class H extends l{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){const{status:t,ctx:r}=this._processInputParams(e);if(r.parsedType!==a.ZodParsedType.object)return(0,o.addIssueToContext)(r,{code:i.ZodIssueCode.invalid_type,expected:a.ZodParsedType.object,received:r.parsedType}),o.INVALID;const s=[],n=this._def.keyType,c=this._def.valueType;for(const e in r.data)s.push({key:n._parse(new d(r,e,r.path,e)),value:c._parse(new d(r,r.data[e],r.path,e))});return r.common.async?o.ParseStatus.mergeObjectAsync(t,s):o.ParseStatus.mergeObjectSync(t,s)}get element(){return this._def.valueType}static create(e,t,r){return new H(t instanceof l?{keyType:e,valueType:t,typeName:ie.ZodRecord,...u(r)}:{keyType:y.create(),valueType:e,typeName:ie.ZodRecord,...u(t)})}}t.ZodRecord=H;class $ extends l{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){const{status:t,ctx:r}=this._processInputParams(e);if(r.parsedType!==a.ZodParsedType.map)return(0,o.addIssueToContext)(r,{code:i.ZodIssueCode.invalid_type,expected:a.ZodParsedType.map,received:r.parsedType}),o.INVALID;const s=this._def.keyType,n=this._def.valueType,c=[...r.data.entries()].map((([e,t],o)=>({key:s._parse(new d(r,e,r.path,[o,"key"])),value:n._parse(new d(r,t,r.path,[o,"value"]))})));if(r.common.async){const e=new Map;return Promise.resolve().then((async()=>{for(const r of c){const s=await r.key,n=await r.value;if("aborted"===s.status||"aborted"===n.status)return o.INVALID;"dirty"!==s.status&&"dirty"!==n.status||t.dirty(),e.set(s.value,n.value)}return{status:t.value,value:e}}))}{const e=new Map;for(const r of c){const s=r.key,n=r.value;if("aborted"===s.status||"aborted"===n.status)return o.INVALID;"dirty"!==s.status&&"dirty"!==n.status||t.dirty(),e.set(s.value,n.value)}return{status:t.value,value:e}}}}t.ZodMap=$,$.create=(e,t,r)=>new $({valueType:t,keyType:e,typeName:ie.ZodMap,...u(r)});class G extends l{_parse(e){const{status:t,ctx:r}=this._processInputParams(e);if(r.parsedType!==a.ZodParsedType.set)return(0,o.addIssueToContext)(r,{code:i.ZodIssueCode.invalid_type,expected:a.ZodParsedType.set,received:r.parsedType}),o.INVALID;const s=this._def;null!==s.minSize&&r.data.size<s.minSize.value&&((0,o.addIssueToContext)(r,{code:i.ZodIssueCode.too_small,minimum:s.minSize.value,type:"set",inclusive:!0,exact:!1,message:s.minSize.message}),t.dirty()),null!==s.maxSize&&r.data.size>s.maxSize.value&&((0,o.addIssueToContext)(r,{code:i.ZodIssueCode.too_big,maximum:s.maxSize.value,type:"set",inclusive:!0,exact:!1,message:s.maxSize.message}),t.dirty());const n=this._def.valueType;function c(e){const r=new Set;for(const s of e){if("aborted"===s.status)return o.INVALID;"dirty"===s.status&&t.dirty(),r.add(s.value)}return{status:t.value,value:r}}const u=[...r.data.values()].map(((e,t)=>n._parse(new d(r,e,r.path,t))));return r.common.async?Promise.all(u).then((e=>c(e))):c(u)}min(e,t){return new G({...this._def,minSize:{value:e,message:n.errorUtil.toString(t)}})}max(e,t){return new G({...this._def,maxSize:{value:e,message:n.errorUtil.toString(t)}})}size(e,t){return this.min(e,t).max(e,t)}nonempty(e){return this.min(1,e)}}t.ZodSet=G,G.create=(e,t)=>new G({valueType:e,minSize:null,maxSize:null,typeName:ie.ZodSet,...u(t)});class F extends l{constructor(){super(...arguments),this.validate=this.implement}_parse(e){const{ctx:t}=this._processInputParams(e);if(t.parsedType!==a.ZodParsedType.function)return(0,o.addIssueToContext)(t,{code:i.ZodIssueCode.invalid_type,expected:a.ZodParsedType.function,received:t.parsedType}),o.INVALID;function r(e,r){return(0,o.makeIssue)({data:e,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,(0,s.getErrorMap)(),s.defaultErrorMap].filter((e=>!!e)),issueData:{code:i.ZodIssueCode.invalid_arguments,argumentsError:r}})}function n(e,r){return(0,o.makeIssue)({data:e,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,(0,s.getErrorMap)(),s.defaultErrorMap].filter((e=>!!e)),issueData:{code:i.ZodIssueCode.invalid_return_type,returnTypeError:r}})}const d={errorMap:t.common.contextualErrorMap},c=t.data;if(this._def.returns instanceof Y){const e=this;return(0,o.OK)((async function(...t){const s=new i.ZodError([]),o=await e._def.args.parseAsync(t,d).catch((e=>{throw s.addIssue(r(t,e)),s})),a=await Reflect.apply(c,this,o);return await e._def.returns._def.type.parseAsync(a,d).catch((e=>{throw s.addIssue(n(a,e)),s}))}))}{const e=this;return(0,o.OK)((function(...t){const s=e._def.args.safeParse(t,d);if(!s.success)throw new i.ZodError([r(t,s.error)]);const o=Reflect.apply(c,this,s.data),a=e._def.returns.safeParse(o,d);if(!a.success)throw new i.ZodError([n(o,a.error)]);return a.data}))}}parameters(){return this._def.args}returnType(){return this._def.returns}args(...e){return new F({...this._def,args:j.create(e).rest(x.create())})}returns(e){return new F({...this._def,returns:e})}implement(e){return this.parse(e)}strictImplement(e){return this.parse(e)}static create(e,t,r){return new F({args:e||j.create([]).rest(x.create()),returns:t||x.create(),typeName:ie.ZodFunction,...u(r)})}}t.ZodFunction=F;class X extends l{get schema(){return this._def.getter()}_parse(e){const{ctx:t}=this._processInputParams(e);return this._def.getter()._parse({data:t.data,path:t.path,parent:t})}}t.ZodLazy=X,X.create=(e,t)=>new X({getter:e,typeName:ie.ZodLazy,...u(t)});class W extends l{_parse(e){if(e.data!==this._def.value){const t=this._getOrReturnCtx(e);return(0,o.addIssueToContext)(t,{received:t.data,code:i.ZodIssueCode.invalid_literal,expected:this._def.value}),o.INVALID}return{status:"valid",value:e.data}}get value(){return this._def.value}}function K(e,t){return new J({values:e,typeName:ie.ZodEnum,...u(t)})}t.ZodLiteral=W,W.create=(e,t)=>new W({value:e,typeName:ie.ZodLiteral,...u(t)});class J extends l{_parse(e){if("string"!=typeof e.data){const t=this._getOrReturnCtx(e),r=this._def.values;return(0,o.addIssueToContext)(t,{expected:a.util.joinValues(r),received:t.parsedType,code:i.ZodIssueCode.invalid_type}),o.INVALID}if(-1===this._def.values.indexOf(e.data)){const t=this._getOrReturnCtx(e),r=this._def.values;return(0,o.addIssueToContext)(t,{received:t.data,code:i.ZodIssueCode.invalid_enum_value,options:r}),o.INVALID}return(0,o.OK)(e.data)}get options(){return this._def.values}get enum(){const e={};for(const t of this._def.values)e[t]=t;return e}get Values(){const e={};for(const t of this._def.values)e[t]=t;return e}get Enum(){const e={};for(const t of this._def.values)e[t]=t;return e}extract(e){return J.create(e)}exclude(e){return J.create(this.options.filter((t=>!e.includes(t))))}}t.ZodEnum=J,J.create=K;class z extends l{_parse(e){const t=a.util.getValidEnumValues(this._def.values),r=this._getOrReturnCtx(e);if(r.parsedType!==a.ZodParsedType.string&&r.parsedType!==a.ZodParsedType.number){const e=a.util.objectValues(t);return(0,o.addIssueToContext)(r,{expected:a.util.joinValues(e),received:r.parsedType,code:i.ZodIssueCode.invalid_type}),o.INVALID}if(-1===t.indexOf(e.data)){const e=a.util.objectValues(t);return(0,o.addIssueToContext)(r,{received:r.data,code:i.ZodIssueCode.invalid_enum_value,options:e}),o.INVALID}return(0,o.OK)(e.data)}get enum(){return this._def.values}}t.ZodNativeEnum=z,z.create=(e,t)=>new z({values:e,typeName:ie.ZodNativeEnum,...u(t)});class Y extends l{unwrap(){return this._def.type}_parse(e){const{ctx:t}=this._processInputParams(e);if(t.parsedType!==a.ZodParsedType.promise&&!1===t.common.async)return(0,o.addIssueToContext)(t,{code:i.ZodIssueCode.invalid_type,expected:a.ZodParsedType.promise,received:t.parsedType}),o.INVALID;const r=t.parsedType===a.ZodParsedType.promise?t.data:Promise.resolve(t.data);return(0,o.OK)(r.then((e=>this._def.type.parseAsync(e,{path:t.path,errorMap:t.common.contextualErrorMap}))))}}t.ZodPromise=Y,Y.create=(e,t)=>new Y({type:e,typeName:ie.ZodPromise,...u(t)});class q extends l{innerType(){return this._def.schema}sourceType(){return this._def.schema._def.typeName===ie.ZodEffects?this._def.schema.sourceType():this._def.schema}_parse(e){const{status:t,ctx:r}=this._processInputParams(e),s=this._def.effect||null,n={addIssue:e=>{(0,o.addIssueToContext)(r,e),e.fatal?t.abort():t.dirty()},get path(){return r.path}};if(n.addIssue=n.addIssue.bind(n),"preprocess"===s.type){const e=s.transform(r.data,n);return r.common.issues.length?{status:"dirty",value:r.data}:r.common.async?Promise.resolve(e).then((e=>this._def.schema._parseAsync({data:e,path:r.path,parent:r}))):this._def.schema._parseSync({data:e,path:r.path,parent:r})}if("refinement"===s.type){const e=e=>{const t=s.refinement(e,n);if(r.common.async)return Promise.resolve(t);if(t instanceof Promise)throw new Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");return e};if(!1===r.common.async){const s=this._def.schema._parseSync({data:r.data,path:r.path,parent:r});return"aborted"===s.status?o.INVALID:("dirty"===s.status&&t.dirty(),e(s.value),{status:t.value,value:s.value})}return this._def.schema._parseAsync({data:r.data,path:r.path,parent:r}).then((r=>"aborted"===r.status?o.INVALID:("dirty"===r.status&&t.dirty(),e(r.value).then((()=>({status:t.value,value:r.value}))))))}if("transform"===s.type){if(!1===r.common.async){const e=this._def.schema._parseSync({data:r.data,path:r.path,parent:r});if(!(0,o.isValid)(e))return e;const a=s.transform(e.value,n);if(a instanceof Promise)throw new Error("Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.");return{status:t.value,value:a}}return this._def.schema._parseAsync({data:r.data,path:r.path,parent:r}).then((e=>(0,o.isValid)(e)?Promise.resolve(s.transform(e.value,n)).then((e=>({status:t.value,value:e}))):e))}a.util.assertNever(s)}}t.ZodEffects=q,t.ZodTransformer=q,q.create=(e,t,r)=>new q({schema:e,typeName:ie.ZodEffects,effect:t,...u(r)}),q.createWithPreprocess=(e,t,r)=>new q({schema:t,effect:{type:"preprocess",transform:e},typeName:ie.ZodEffects,...u(r)});class Q extends l{_parse(e){return this._getType(e)===a.ZodParsedType.undefined?(0,o.OK)(void 0):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}t.ZodOptional=Q,Q.create=(e,t)=>new Q({innerType:e,typeName:ie.ZodOptional,...u(t)});class ee extends l{_parse(e){return this._getType(e)===a.ZodParsedType.null?(0,o.OK)(null):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}t.ZodNullable=ee,ee.create=(e,t)=>new ee({innerType:e,typeName:ie.ZodNullable,...u(t)});class te extends l{_parse(e){const{ctx:t}=this._processInputParams(e);let r=t.data;return t.parsedType===a.ZodParsedType.undefined&&(r=this._def.defaultValue()),this._def.innerType._parse({data:r,path:t.path,parent:t})}removeDefault(){return this._def.innerType}}t.ZodDefault=te,te.create=(e,t)=>new te({innerType:e,typeName:ie.ZodDefault,defaultValue:"function"==typeof t.default?t.default:()=>t.default,...u(t)});class re extends l{_parse(e){const{ctx:t}=this._processInputParams(e),r={...t,common:{...t.common,issues:[]}},s=this._def.innerType._parse({data:r.data,path:r.path,parent:{...r}});return(0,o.isAsync)(s)?s.then((e=>({status:"valid",value:"valid"===e.status?e.value:this._def.catchValue({get error(){return new i.ZodError(r.common.issues)},input:r.data})}))):{status:"valid",value:"valid"===s.status?s.value:this._def.catchValue({get error(){return new i.ZodError(r.common.issues)},input:r.data})}}removeCatch(){return this._def.innerType}}t.ZodCatch=re,re.create=(e,t)=>new re({innerType:e,typeName:ie.ZodCatch,catchValue:"function"==typeof t.catch?t.catch:()=>t.catch,...u(t)});class se extends l{_parse(e){if(this._getType(e)!==a.ZodParsedType.nan){const t=this._getOrReturnCtx(e);return(0,o.addIssueToContext)(t,{code:i.ZodIssueCode.invalid_type,expected:a.ZodParsedType.nan,received:t.parsedType}),o.INVALID}return{status:"valid",value:e.data}}}t.ZodNaN=se,se.create=e=>new se({typeName:ie.ZodNaN,...u(e)}),t.BRAND=Symbol("zod_brand");class ne extends l{_parse(e){const{ctx:t}=this._processInputParams(e),r=t.data;return this._def.type._parse({data:r,path:t.path,parent:t})}unwrap(){return this._def.type}}t.ZodBranded=ne;class oe extends l{_parse(e){const{status:t,ctx:r}=this._processInputParams(e);if(r.common.async)return(async()=>{const e=await this._def.in._parseAsync({data:r.data,path:r.path,parent:r});return"aborted"===e.status?o.INVALID:"dirty"===e.status?(t.dirty(),(0,o.DIRTY)(e.value)):this._def.out._parseAsync({data:e.value,path:r.path,parent:r})})();{const e=this._def.in._parseSync({data:r.data,path:r.path,parent:r});return"aborted"===e.status?o.INVALID:"dirty"===e.status?(t.dirty(),{status:"dirty",value:e.value}):this._def.out._parseSync({data:e.value,path:r.path,parent:r})}}static create(e,t){return new oe({in:e,out:t,typeName:ie.ZodPipeline})}}t.ZodPipeline=oe;class ae extends l{_parse(e){const t=this._def.innerType._parse(e);return(0,o.isValid)(t)&&(t.value=Object.freeze(t.value)),t}}var ie;t.ZodReadonly=ae,ae.create=(e,t)=>new ae({innerType:e,typeName:ie.ZodReadonly,...u(t)}),t.custom=(e,t={},r)=>e?S.create().superRefine(((s,n)=>{var o,a;if(!e(s)){const e="function"==typeof t?t(s):"string"==typeof t?{message:t}:t,i=null===(a=null!==(o=e.fatal)&&void 0!==o?o:r)||void 0===a||a,d="string"==typeof e?{message:e}:e;n.addIssue({code:"custom",...d,fatal:i})}})):S.create(),t.late={object:L.lazycreate},function(e){e.ZodString="ZodString",e.ZodNumber="ZodNumber",e.ZodNaN="ZodNaN",e.ZodBigInt="ZodBigInt",e.ZodBoolean="ZodBoolean",e.ZodDate="ZodDate",e.ZodSymbol="ZodSymbol",e.ZodUndefined="ZodUndefined",e.ZodNull="ZodNull",e.ZodAny="ZodAny",e.ZodUnknown="ZodUnknown",e.ZodNever="ZodNever",e.ZodVoid="ZodVoid",e.ZodArray="ZodArray",e.ZodObject="ZodObject",e.ZodUnion="ZodUnion",e.ZodDiscriminatedUnion="ZodDiscriminatedUnion",e.ZodIntersection="ZodIntersection",e.ZodTuple="ZodTuple",e.ZodRecord="ZodRecord",e.ZodMap="ZodMap",e.ZodSet="ZodSet",e.ZodFunction="ZodFunction",e.ZodLazy="ZodLazy",e.ZodLiteral="ZodLiteral",e.ZodEnum="ZodEnum",e.ZodEffects="ZodEffects",e.ZodNativeEnum="ZodNativeEnum",e.ZodOptional="ZodOptional",e.ZodNullable="ZodNullable",e.ZodDefault="ZodDefault",e.ZodCatch="ZodCatch",e.ZodPromise="ZodPromise",e.ZodBranded="ZodBranded",e.ZodPipeline="ZodPipeline",e.ZodReadonly="ZodReadonly"}(ie=t.ZodFirstPartyTypeKind||(t.ZodFirstPartyTypeKind={})),t.instanceof=(e,r={message:`Input not instance of ${e.name}`})=>(0,t.custom)((t=>t instanceof e),r);const de=y.create;t.string=de;const ce=v.create;t.number=ce;const ue=se.create;t.nan=ue;const le=g.create;t.bigint=le;const _e=N.create;t.boolean=_e;const pe=b.create;t.date=pe;const he=O.create;t.symbol=he;const Ee=A.create;t.undefined=Ee;const Re=C.create;t.null=Re;const fe=S.create;t.any=fe;const me=x.create;t.unknown=me;const Ie=P.create;t.never=Ie;const ye=D.create;t.void=ye;const Te=M.create;t.array=Te;const ve=L.create;t.object=ve;const ge=L.strictCreate;t.strictObject=ge;const Ne=w.create;t.union=Ne;const be=U.create;t.discriminatedUnion=be;const Oe=B.create;t.intersection=Oe;const Ae=j.create;t.tuple=Ae;const Ce=H.create;t.record=Ce;const Se=$.create;t.map=Se;const xe=G.create;t.set=xe;const Pe=F.create;t.function=Pe;const De=X.create;t.lazy=De;const Me=W.create;t.literal=Me;const ke=J.create;t.enum=ke;const Le=z.create;t.nativeEnum=Le;const we=Y.create;t.promise=we;const Ze=q.create;t.effect=Ze,t.transformer=Ze;const Ue=Q.create;t.optional=Ue;const Ve=ee.create;t.nullable=Ve;const Be=q.createWithPreprocess;t.preprocess=Be;const je=oe.create;t.pipeline=je,t.ostring=()=>de().optional(),t.onumber=()=>ce().optional(),t.oboolean=()=>_e().optional(),t.coerce={string:e=>y.create({...e,coerce:!0}),number:e=>v.create({...e,coerce:!0}),boolean:e=>N.create({...e,coerce:!0}),bigint:e=>g.create({...e,coerce:!0}),date:e=>b.create({...e,coerce:!0})},t.NEVER=o.INVALID},7320:(e,t)=>{function r(e){if(!Number.isSafeInteger(e)||e<0)throw new Error(`Wrong positive integer: ${e}`)}function s(e){if("boolean"!=typeof e)throw new Error(`Expected boolean, not ${e}`)}function n(e,...t){if(!(e instanceof Uint8Array))throw new TypeError("Expected Uint8Array");if(t.length>0&&!t.includes(e.length))throw new TypeError(`Expected Uint8Array of length ${t}, not of length=${e.length}`)}function o(e){if("function"!=typeof e||"function"!=typeof e.create)throw new Error("Hash should be wrapped by utils.wrapConstructor");r(e.outputLen),r(e.blockLen)}function a(e,t=!0){if(e.destroyed)throw new Error("Hash instance has been destroyed");if(t&&e.finished)throw new Error("Hash#digest() has already been called")}function i(e,t){n(e);const r=t.outputLen;if(e.length<r)throw new Error(`digestInto() expects output buffer of length at least ${r}`)}Object.defineProperty(t,"__esModule",{value:!0}),t.output=t.exists=t.hash=t.bytes=t.bool=t.number=void 0,t.number=r,t.bool=s,t.bytes=n,t.hash=o,t.exists=a,t.output=i;const d={number:r,bool:s,bytes:n,hash:o,exists:a,output:i};t.default=d},6873:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.add=t.toBig=t.split=t.fromBig=void 0;const r=BigInt(2**32-1),s=BigInt(32);function n(e,t=!1){return t?{h:Number(e&r),l:Number(e>>s&r)}:{h:0|Number(e>>s&r),l:0|Number(e&r)}}function o(e,t=!1){let r=new Uint32Array(e.length),s=new Uint32Array(e.length);for(let o=0;o<e.length;o++){const{h:a,l:i}=n(e[o],t);[r[o],s[o]]=[a,i]}return[r,s]}function a(e,t,r,s){const n=(t>>>0)+(s>>>0);return{h:e+r+(n/2**32|0)|0,l:0|n}}t.fromBig=n,t.split=o,t.toBig=(e,t)=>BigInt(e>>>0)<<s|BigInt(t>>>0),t.add=a;const i={fromBig:n,split:o,toBig:t.toBig,shrSH:(e,t,r)=>e>>>r,shrSL:(e,t,r)=>e<<32-r|t>>>r,rotrSH:(e,t,r)=>e>>>r|t<<32-r,rotrSL:(e,t,r)=>e<<32-r|t>>>r,rotrBH:(e,t,r)=>e<<64-r|t>>>r-32,rotrBL:(e,t,r)=>e>>>r-32|t<<64-r,rotr32H:(e,t)=>t,rotr32L:(e,t)=>e,rotlSH:(e,t,r)=>e<<r|t>>>32-r,rotlSL:(e,t,r)=>t<<r|e>>>32-r,rotlBH:(e,t,r)=>t<<r-32|e>>>64-r,rotlBL:(e,t,r)=>e<<r-32|t>>>64-r,add:a,add3L:(e,t,r)=>(e>>>0)+(t>>>0)+(r>>>0),add3H:(e,t,r,s)=>t+r+s+(e/2**32|0)|0,add4L:(e,t,r,s)=>(e>>>0)+(t>>>0)+(r>>>0)+(s>>>0),add4H:(e,t,r,s,n)=>t+r+s+n+(e/2**32|0)|0,add5H:(e,t,r,s,n,o)=>t+r+s+n+o+(e/2**32|0)|0,add5L:(e,t,r,s,n)=>(e>>>0)+(t>>>0)+(r>>>0)+(s>>>0)+(n>>>0)};t.default=i},1945:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.crypto=void 0,t.crypto="object"==typeof globalThis&&"crypto"in globalThis?globalThis.crypto:void 0},5426:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.shake256=t.shake128=t.keccak_512=t.keccak_384=t.keccak_256=t.keccak_224=t.sha3_512=t.sha3_384=t.sha3_256=t.sha3_224=t.Keccak=t.keccakP=void 0;const s=r(7320),n=r(6873),o=r(8089),[a,i,d]=[[],[],[]],c=BigInt(0),u=BigInt(1),l=BigInt(2),_=BigInt(7),p=BigInt(256),h=BigInt(113);for(let e=0,t=u,r=1,s=0;e<24;e++){[r,s]=[s,(2*r+3*s)%5],a.push(2*(5*s+r)),i.push((e+1)*(e+2)/2%64);let n=c;for(let e=0;e<7;e++)t=(t<<u^(t>>_)*h)%p,t&l&&(n^=u<<(u<<BigInt(e))-u);d.push(n)}const[E,R]=n.default.split(d,!0),f=(e,t,r)=>r>32?n.default.rotlBH(e,t,r):n.default.rotlSH(e,t,r),m=(e,t,r)=>r>32?n.default.rotlBL(e,t,r):n.default.rotlSL(e,t,r);function I(e,t=24){const r=new Uint32Array(10);for(let s=24-t;s<24;s++){for(let t=0;t<10;t++)r[t]=e[t]^e[t+10]^e[t+20]^e[t+30]^e[t+40];for(let t=0;t<10;t+=2){const s=(t+8)%10,n=(t+2)%10,o=r[n],a=r[n+1],i=f(o,a,1)^r[s],d=m(o,a,1)^r[s+1];for(let r=0;r<50;r+=10)e[t+r]^=i,e[t+r+1]^=d}let t=e[2],n=e[3];for(let r=0;r<24;r++){const s=i[r],o=f(t,n,s),d=m(t,n,s),c=a[r];t=e[c],n=e[c+1],e[c]=o,e[c+1]=d}for(let t=0;t<50;t+=10){for(let s=0;s<10;s++)r[s]=e[t+s];for(let s=0;s<10;s++)e[t+s]^=~r[(s+2)%10]&r[(s+4)%10]}e[0]^=E[s],e[1]^=R[s]}r.fill(0)}t.keccakP=I;class y extends o.Hash{constructor(e,t,r,n=!1,a=24){if(super(),this.blockLen=e,this.suffix=t,this.outputLen=r,this.enableXOF=n,this.rounds=a,this.pos=0,this.posOut=0,this.finished=!1,this.destroyed=!1,s.default.number(r),0>=this.blockLen||this.blockLen>=200)throw new Error("Sha3 supports only keccak-f1600 function");this.state=new Uint8Array(200),this.state32=(0,o.u32)(this.state)}keccak(){I(this.state32,this.rounds),this.posOut=0,this.pos=0}update(e){s.default.exists(this);const{blockLen:t,state:r}=this,n=(e=(0,o.toBytes)(e)).length;for(let s=0;s<n;){const o=Math.min(t-this.pos,n-s);for(let t=0;t<o;t++)r[this.pos++]^=e[s++];this.pos===t&&this.keccak()}return this}finish(){if(this.finished)return;this.finished=!0;const{state:e,suffix:t,pos:r,blockLen:s}=this;e[r]^=t,0!=(128&t)&&r===s-1&&this.keccak(),e[s-1]^=128,this.keccak()}writeInto(e){s.default.exists(this,!1),s.default.bytes(e),this.finish();const t=this.state,{blockLen:r}=this;for(let s=0,n=e.length;s<n;){this.posOut>=r&&this.keccak();const o=Math.min(r-this.posOut,n-s);e.set(t.subarray(this.posOut,this.posOut+o),s),this.posOut+=o,s+=o}return e}xofInto(e){if(!this.enableXOF)throw new Error("XOF is not possible for this instance");return this.writeInto(e)}xof(e){return s.default.number(e),this.xofInto(new Uint8Array(e))}digestInto(e){if(s.default.output(e,this),this.finished)throw new Error("digest() was already called");return this.writeInto(e),this.destroy(),e}digest(){return this.digestInto(new Uint8Array(this.outputLen))}destroy(){this.destroyed=!0,this.state.fill(0)}_cloneInto(e){const{blockLen:t,suffix:r,outputLen:s,rounds:n,enableXOF:o}=this;return e||(e=new y(t,r,s,o,n)),e.state32.set(this.state32),e.pos=this.pos,e.posOut=this.posOut,e.finished=this.finished,e.rounds=n,e.suffix=r,e.outputLen=s,e.enableXOF=o,e.destroyed=this.destroyed,e}}t.Keccak=y;const T=(e,t,r)=>(0,o.wrapConstructor)((()=>new y(t,e,r)));t.sha3_224=T(6,144,28),t.sha3_256=T(6,136,32),t.sha3_384=T(6,104,48),t.sha3_512=T(6,72,64),t.keccak_224=T(1,144,28),t.keccak_256=T(1,136,32),t.keccak_384=T(1,104,48),t.keccak_512=T(1,72,64);const v=(e,t,r)=>(0,o.wrapConstructorWithOpts)(((s={})=>new y(t,e,void 0===s.dkLen?r:s.dkLen,!0)));t.shake128=v(31,168,16),t.shake256=v(31,136,32)},8089:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.randomBytes=t.wrapConstructorWithOpts=t.wrapConstructor=t.checkOpts=t.Hash=t.concatBytes=t.toBytes=t.utf8ToBytes=t.asyncLoop=t.nextTick=t.hexToBytes=t.bytesToHex=t.isLE=t.rotr=t.createView=t.u32=t.u8=void 0;const s=r(1945);if(t.u8=e=>new Uint8Array(e.buffer,e.byteOffset,e.byteLength),t.u32=e=>new Uint32Array(e.buffer,e.byteOffset,Math.floor(e.byteLength/4)),t.createView=e=>new DataView(e.buffer,e.byteOffset,e.byteLength),t.rotr=(e,t)=>e<<32-t|e>>>t,t.isLE=68===new Uint8Array(new Uint32Array([287454020]).buffer)[0],!t.isLE)throw new Error("Non little-endian hardware is not supported");const n=Array.from({length:256},((e,t)=>t.toString(16).padStart(2,"0")));function o(e){if("string"!=typeof e)throw new TypeError("utf8ToBytes expected string, got "+typeof e);return(new TextEncoder).encode(e)}function a(e){if("string"==typeof e&&(e=o(e)),!(e instanceof Uint8Array))throw new TypeError(`Expected input type is Uint8Array (got ${typeof e})`);return e}t.bytesToHex=function(e){if(!(e instanceof Uint8Array))throw new Error("Uint8Array expected");let t="";for(let r=0;r<e.length;r++)t+=n[e[r]];return t},t.hexToBytes=function(e){if("string"!=typeof e)throw new TypeError("hexToBytes: expected string, got "+typeof e);if(e.length%2)throw new Error("hexToBytes: received invalid unpadded hex");const t=new Uint8Array(e.length/2);for(let r=0;r<t.length;r++){const s=2*r,n=e.slice(s,s+2),o=Number.parseInt(n,16);if(Number.isNaN(o)||o<0)throw new Error("Invalid byte sequence");t[r]=o}return t},t.nextTick=async()=>{},t.asyncLoop=async function(e,r,s){let n=Date.now();for(let o=0;o<e;o++){s(o);const e=Date.now()-n;e>=0&&e<r||(await(0,t.nextTick)(),n+=e)}},t.utf8ToBytes=o,t.toBytes=a,t.concatBytes=function(...e){if(!e.every((e=>e instanceof Uint8Array)))throw new Error("Uint8Array list expected");if(1===e.length)return e[0];const t=e.reduce(((e,t)=>e+t.length),0),r=new Uint8Array(t);for(let t=0,s=0;t<e.length;t++){const n=e[t];r.set(n,s),s+=n.length}return r},t.Hash=class{clone(){return this._cloneInto()}},t.checkOpts=function(e,t){if(void 0!==t&&("object"!=typeof t||(r=t,"[object Object]"!==Object.prototype.toString.call(r)||r.constructor!==Object)))throw new TypeError("Options should be object or undefined");var r;return Object.assign(e,t)},t.wrapConstructor=function(e){const t=t=>e().update(a(t)).digest(),r=e();return t.outputLen=r.outputLen,t.blockLen=r.blockLen,t.create=()=>e(),t},t.wrapConstructorWithOpts=function(e){const t=(t,r)=>e(r).update(a(t)).digest(),r=e({});return t.outputLen=r.outputLen,t.blockLen=r.blockLen,t.create=t=>e(t),t},t.randomBytes=function(e=32){if(s.crypto&&"function"==typeof s.crypto.getRandomValues)return s.crypto.getRandomValues(new Uint8Array(e));throw new Error("crypto.getRandomValues must be defined")}},2192:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.keccak512=t.keccak384=t.keccak256=t.keccak224=void 0;const s=r(5426),n=r(8040);t.keccak224=(0,n.wrapHash)(s.keccak_224),t.keccak256=(()=>{const e=(0,n.wrapHash)(s.keccak_256);return e.create=s.keccak_256.create,e})(),t.keccak384=(0,n.wrapHash)(s.keccak_384),t.keccak512=(0,n.wrapHash)(s.keccak_512)},8040:function(e,t,r){e=r.nmd(e);var s=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.crypto=t.wrapHash=t.equalsBytes=t.hexToBytes=t.bytesToUtf8=t.utf8ToBytes=t.createView=t.concatBytes=t.toHex=t.bytesToHex=t.assertBytes=t.assertBool=void 0;const n=s(r(7320)),o=r(8089),a=n.default.bool;t.assertBool=a;const i=n.default.bytes;t.assertBytes=i;var d=r(8089);Object.defineProperty(t,"bytesToHex",{enumerable:!0,get:function(){return d.bytesToHex}}),Object.defineProperty(t,"toHex",{enumerable:!0,get:function(){return d.bytesToHex}}),Object.defineProperty(t,"concatBytes",{enumerable:!0,get:function(){return d.concatBytes}}),Object.defineProperty(t,"createView",{enumerable:!0,get:function(){return d.createView}}),Object.defineProperty(t,"utf8ToBytes",{enumerable:!0,get:function(){return d.utf8ToBytes}}),t.bytesToUtf8=function(e){if(!(e instanceof Uint8Array))throw new TypeError("bytesToUtf8 expected Uint8Array, got "+typeof e);return(new TextDecoder).decode(e)},t.hexToBytes=function(e){const t=e.startsWith("0x")?e.substring(2):e;return(0,o.hexToBytes)(t)},t.equalsBytes=function(e,t){if(e.length!==t.length)return!1;for(let r=0;r<e.length;r++)if(e[r]!==t[r])return!1;return!0},t.wrapHash=function(e){return t=>(n.default.bytes(t),e(t))},t.crypto=(()=>{const t="object"==typeof self&&"crypto"in self?self.crypto:void 0,r="function"==typeof e.require&&e.require.bind(e);return{node:r&&!t?r("crypto"):void 0,web:t}})()},7639:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ERR_TX_POLLING_TIMEOUT=t.ERR_TX_DATA_AND_INPUT=t.ERR_TX_UNSUPPORTED_TYPE=t.ERR_TX_UNSUPPORTED_EIP_1559=t.ERR_TX_UNABLE_TO_POPULATE_NONCE=t.ERR_TX_INVALID_NONCE_OR_CHAIN_ID=t.ERR_TX_INVALID_OBJECT=t.ERR_TX_INVALID_LEGACY_FEE_MARKET=t.ERR_TX_INVALID_FEE_MARKET_GAS_PRICE=t.ERR_TX_INVALID_FEE_MARKET_GAS=t.ERR_TX_INVALID_LEGACY_GAS=t.ERR_TX_MISSING_GAS=t.ERR_TX_MISSING_CHAIN_INFO=t.ERR_TX_INVALID_CHAIN_INFO=t.ERR_TX_CHAIN_ID_MISMATCH=t.ERR_TX_MISSING_CUSTOM_CHAIN_ID=t.ERR_TX_MISSING_CUSTOM_CHAIN=t.ERR_TX_INVALID_CALL=t.ERR_TX_INVALID_SENDER=t.ERR_RAW_TX_UNDEFINED=t.ERR_TX_OUT_OF_GAS=t.ERR_TX_REVERT_WITHOUT_REASON=t.ERR_TX_CONTRACT_NOT_STORED=t.ERR_TX_NO_CONTRACT_ADDRESS=t.ERR_TX_REVERT_TRANSACTION=t.ERR_TX_REVERT_INSTRUCTION=t.ERR_TX=t.ERR_CONTRACT_TX_DATA_AND_INPUT=t.ERR_CONTRACT_EXECUTION_REVERTED=t.ERR_CONTRACT_INSTANTIATION=t.ERR_CONTRACT_MISSING_FROM_ADDRESS=t.ERR_CONTRACT_MISSING_ADDRESS=t.ERR_CONTRACT_MISSING_DEPLOY_DATA=t.ERR_CONTRACT_RESERVED_EVENT=t.ERR_CONTRACT_EVENT_NOT_EXISTS=t.ERR_CONTRACT_REQUIRED_CALLBACK=t.ERR_CONTRACT_ABI_MISSING=t.ERR_CONTRACT_RESOLVER_MISSING=t.ERR_CONTRACT=t.ERR_MULTIPLE_ERRORS=t.ERR_INVALID_METHOD_PARAMS=t.ERR_EXISTING_PLUGIN_NAMESPACE=t.ERR_ABI_ENCODING=t.ERR_OPERATION_ABORT=t.ERR_OPERATION_TIMEOUT=t.ERR_METHOD_NOT_IMPLEMENTED=t.ERR_FORMATTERS=t.ERR_PARAM=t.ERR_INVALID_RESPONSE=t.ERR_RESPONSE=void 0,t.ERR_INVALID_BYTES=t.ERR_INVALID_STRING=t.ERR_ENS_NETWORK_NOT_SYNCED=t.ERR_ENS_UNSUPPORTED_NETWORK=t.ERR_ENS_CHECK_INTERFACE_SUPPORT=t.JSONRPC_ERR_CHAIN_DISCONNECTED=t.JSONRPC_ERR_DISCONNECTED=t.JSONRPC_ERR_UNSUPPORTED_METHOD=t.JSONRPC_ERR_UNAUTHORIZED=t.JSONRPC_ERR_REJECTED_REQUEST=t.GENESIS_BLOCK_NUMBER=t.ERR_INVALID_SIGNATURE=t.ERR_SIGNATURE_FAILED=t.ERR_PBKDF2_ITERATIONS=t.ERR_INVALID_KEYSTORE=t.ERR_IV_LENGTH=t.ERR_INVALID_PASSWORD=t.ERR_KEY_VERSION_UNSUPPORTED=t.ERR_KEY_DERIVATION_FAIL=t.ERR_UNSUPPORTED_KDF=t.ERR_INVALID_PRIVATE_KEY=t.ERR_PRIVATE_KEY_LENGTH=t.ERR_WS_PROVIDER=t.ERR_SUBSCRIPTION=t.ERR_INVALID_CLIENT=t.ERR_INVALID_PROVIDER=t.ERR_PROVIDER=t.ERR_REQ_ALREADY_SENT=t.ERR_CONN_PENDING_REQUESTS=t.ERR_CONN_MAX_ATTEMPTS=t.ERR_CONN_CLOSE=t.ERR_CONN_NOT_OPEN=t.ERR_CONN_TIMEOUT=t.ERR_CONN_INVALID=t.ERR_CONN=t.ERR_TX_GAS_MISMATCH_INNER_ERROR=t.ERR_TX_MISSING_GAS_INNER_ERROR=t.ERR_TX_INVALID_PROPERTIES_FOR_TYPE=t.ERR_TX_REVERT_TRANSACTION_CUSTOM_ERROR=t.ERR_TX_INVALID_RECEIVER=t.ERR_TX_HARDFORK_MISMATCH=t.ERR_TX_CHAIN_MISMATCH=t.ERR_TX_GAS_MISMATCH=t.ERR_TX_SIGNING=t.ERR_TX_BLOCK_TIMEOUT=t.ERR_TX_SEND_TIMEOUT=t.ERR_TX_NOT_FOUND=t.ERR_TX_LOCAL_WALLET_NOT_AVAILABLE=t.ERR_TX_RECEIPT_MISSING_BLOCK_NUMBER=t.ERR_TX_RECEIPT_MISSING_OR_BLOCKHASH_NULL=void 0,t.ERR_RPC_NOT_SUPPORTED=t.ERR_RPC_LIMIT_EXCEEDED=t.ERR_RPC_UNSUPPORTED_METHOD=t.ERR_RPC_TRANSACTION_REJECTED=t.ERR_RPC_UNAVAILABLE_RESOURCE=t.ERR_RPC_MISSING_RESOURCE=t.ERR_RPC_INVALID_INPUT=t.ERR_RPC_INTERNAL_ERROR=t.ERR_RPC_INVALID_PARAMS=t.ERR_RPC_INVALID_METHOD=t.ERR_RPC_INVALID_REQUEST=t.ERR_RPC_INVALID_JSON=t.ERR_SCHEMA_FORMAT=t.ERR_CORE_CHAIN_MISMATCH=t.ERR_CORE_HARDFORK_MISMATCH=t.ERR_VALIDATION=t.ERR_INVALID_INTEGER=t.ERR_INVALID_NIBBLE_WIDTH=t.ERR_INVALID_TYPE_ABI=t.ERR_INVALID_BLOCK=t.ERR_INVALID_LARGE_VALUE=t.ERR_INVALID_SIZE=t.ERR_INVALID_UNSIGNED_INTEGER=t.ERR_INVALID_BOOLEAN=t.ERR_INVALID_TYPE=t.ERR_INVALID_HEX=t.ERR_INVALID_ADDRESS=t.ERR_INVALID_UNIT=t.ERR_INVALID_NUMBER=void 0,t.ERR_RESPONSE=100,t.ERR_INVALID_RESPONSE=101,t.ERR_PARAM=200,t.ERR_FORMATTERS=201,t.ERR_METHOD_NOT_IMPLEMENTED=202,t.ERR_OPERATION_TIMEOUT=203,t.ERR_OPERATION_ABORT=204,t.ERR_ABI_ENCODING=205,t.ERR_EXISTING_PLUGIN_NAMESPACE=206,t.ERR_INVALID_METHOD_PARAMS=207,t.ERR_MULTIPLE_ERRORS=208,t.ERR_CONTRACT=300,t.ERR_CONTRACT_RESOLVER_MISSING=301,t.ERR_CONTRACT_ABI_MISSING=302,t.ERR_CONTRACT_REQUIRED_CALLBACK=303,t.ERR_CONTRACT_EVENT_NOT_EXISTS=304,t.ERR_CONTRACT_RESERVED_EVENT=305,t.ERR_CONTRACT_MISSING_DEPLOY_DATA=306,t.ERR_CONTRACT_MISSING_ADDRESS=307,t.ERR_CONTRACT_MISSING_FROM_ADDRESS=308,t.ERR_CONTRACT_INSTANTIATION=309,t.ERR_CONTRACT_EXECUTION_REVERTED=310,t.ERR_CONTRACT_TX_DATA_AND_INPUT=311,t.ERR_TX=400,t.ERR_TX_REVERT_INSTRUCTION=401,t.ERR_TX_REVERT_TRANSACTION=402,t.ERR_TX_NO_CONTRACT_ADDRESS=403,t.ERR_TX_CONTRACT_NOT_STORED=404,t.ERR_TX_REVERT_WITHOUT_REASON=405,t.ERR_TX_OUT_OF_GAS=406,t.ERR_RAW_TX_UNDEFINED=407,t.ERR_TX_INVALID_SENDER=408,t.ERR_TX_INVALID_CALL=409,t.ERR_TX_MISSING_CUSTOM_CHAIN=410,t.ERR_TX_MISSING_CUSTOM_CHAIN_ID=411,t.ERR_TX_CHAIN_ID_MISMATCH=412,t.ERR_TX_INVALID_CHAIN_INFO=413,t.ERR_TX_MISSING_CHAIN_INFO=414,t.ERR_TX_MISSING_GAS=415,t.ERR_TX_INVALID_LEGACY_GAS=416,t.ERR_TX_INVALID_FEE_MARKET_GAS=417,t.ERR_TX_INVALID_FEE_MARKET_GAS_PRICE=418,t.ERR_TX_INVALID_LEGACY_FEE_MARKET=419,t.ERR_TX_INVALID_OBJECT=420,t.ERR_TX_INVALID_NONCE_OR_CHAIN_ID=421,t.ERR_TX_UNABLE_TO_POPULATE_NONCE=422,t.ERR_TX_UNSUPPORTED_EIP_1559=423,t.ERR_TX_UNSUPPORTED_TYPE=424,t.ERR_TX_DATA_AND_INPUT=425,t.ERR_TX_POLLING_TIMEOUT=426,t.ERR_TX_RECEIPT_MISSING_OR_BLOCKHASH_NULL=427,t.ERR_TX_RECEIPT_MISSING_BLOCK_NUMBER=428,t.ERR_TX_LOCAL_WALLET_NOT_AVAILABLE=429,t.ERR_TX_NOT_FOUND=430,t.ERR_TX_SEND_TIMEOUT=431,t.ERR_TX_BLOCK_TIMEOUT=432,t.ERR_TX_SIGNING=433,t.ERR_TX_GAS_MISMATCH=434,t.ERR_TX_CHAIN_MISMATCH=435,t.ERR_TX_HARDFORK_MISMATCH=436,t.ERR_TX_INVALID_RECEIVER=437,t.ERR_TX_REVERT_TRANSACTION_CUSTOM_ERROR=438,t.ERR_TX_INVALID_PROPERTIES_FOR_TYPE=439,t.ERR_TX_MISSING_GAS_INNER_ERROR=440,t.ERR_TX_GAS_MISMATCH_INNER_ERROR=441,t.ERR_CONN=500,t.ERR_CONN_INVALID=501,t.ERR_CONN_TIMEOUT=502,t.ERR_CONN_NOT_OPEN=503,t.ERR_CONN_CLOSE=504,t.ERR_CONN_MAX_ATTEMPTS=505,t.ERR_CONN_PENDING_REQUESTS=506,t.ERR_REQ_ALREADY_SENT=507,t.ERR_PROVIDER=600,t.ERR_INVALID_PROVIDER=601,t.ERR_INVALID_CLIENT=602,t.ERR_SUBSCRIPTION=603,t.ERR_WS_PROVIDER=604,t.ERR_PRIVATE_KEY_LENGTH=701,t.ERR_INVALID_PRIVATE_KEY=702,t.ERR_UNSUPPORTED_KDF=703,t.ERR_KEY_DERIVATION_FAIL=704,t.ERR_KEY_VERSION_UNSUPPORTED=705,t.ERR_INVALID_PASSWORD=706,t.ERR_IV_LENGTH=707,t.ERR_INVALID_KEYSTORE=708,t.ERR_PBKDF2_ITERATIONS=709,t.ERR_SIGNATURE_FAILED=801,t.ERR_INVALID_SIGNATURE=802,t.GENESIS_BLOCK_NUMBER="0x0",t.JSONRPC_ERR_REJECTED_REQUEST=4001,t.JSONRPC_ERR_UNAUTHORIZED=4100,t.JSONRPC_ERR_UNSUPPORTED_METHOD=4200,t.JSONRPC_ERR_DISCONNECTED=4900,t.JSONRPC_ERR_CHAIN_DISCONNECTED=4901,t.ERR_ENS_CHECK_INTERFACE_SUPPORT=901,t.ERR_ENS_UNSUPPORTED_NETWORK=902,t.ERR_ENS_NETWORK_NOT_SYNCED=903,t.ERR_INVALID_STRING=1001,t.ERR_INVALID_BYTES=1002,t.ERR_INVALID_NUMBER=1003,t.ERR_INVALID_UNIT=1004,t.ERR_INVALID_ADDRESS=1005,t.ERR_INVALID_HEX=1006,t.ERR_INVALID_TYPE=1007,t.ERR_INVALID_BOOLEAN=1008,t.ERR_INVALID_UNSIGNED_INTEGER=1009,t.ERR_INVALID_SIZE=1010,t.ERR_INVALID_LARGE_VALUE=1011,t.ERR_INVALID_BLOCK=1012,t.ERR_INVALID_TYPE_ABI=1013,t.ERR_INVALID_NIBBLE_WIDTH=1014,t.ERR_INVALID_INTEGER=1015,t.ERR_VALIDATION=1100,t.ERR_CORE_HARDFORK_MISMATCH=1101,t.ERR_CORE_CHAIN_MISMATCH=1102,t.ERR_SCHEMA_FORMAT=1200,t.ERR_RPC_INVALID_JSON=-32700,t.ERR_RPC_INVALID_REQUEST=-32600,t.ERR_RPC_INVALID_METHOD=-32601,t.ERR_RPC_INVALID_PARAMS=-32602,t.ERR_RPC_INTERNAL_ERROR=-32603,t.ERR_RPC_INVALID_INPUT=-32e3,t.ERR_RPC_MISSING_RESOURCE=-32001,t.ERR_RPC_UNAVAILABLE_RESOURCE=-32002,t.ERR_RPC_TRANSACTION_REJECTED=-32003,t.ERR_RPC_UNSUPPORTED_METHOD=-32004,t.ERR_RPC_LIMIT_EXCEEDED=-32005,t.ERR_RPC_NOT_SUPPORTED=-32006},8105:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.PBKDF2IterationsError=t.IVLengthError=t.InvalidPasswordError=t.KeyStoreVersionError=t.KeyDerivationError=t.InvalidKdfError=t.InvalidSignatureError=t.InvalidPrivateKeyError=t.PrivateKeyLengthError=void 0;const s=r(7639),n=r(3685);class o extends n.BaseWeb3Error{constructor(){super("Private key must be 32 bytes."),this.code=s.ERR_PRIVATE_KEY_LENGTH}}t.PrivateKeyLengthError=o;class a extends n.BaseWeb3Error{constructor(){super("Invalid Private Key, Not a valid string or uint8Array"),this.code=s.ERR_INVALID_PRIVATE_KEY}}t.InvalidPrivateKeyError=a;class i extends n.BaseWeb3Error{constructor(e){super(`"${e}"`),this.code=s.ERR_INVALID_SIGNATURE}}t.InvalidSignatureError=i;class d extends n.BaseWeb3Error{constructor(){super("Invalid key derivation function"),this.code=s.ERR_UNSUPPORTED_KDF}}t.InvalidKdfError=d;class c extends n.BaseWeb3Error{constructor(){super("Key derivation failed - possibly wrong password"),this.code=s.ERR_KEY_DERIVATION_FAIL}}t.KeyDerivationError=c;class u extends n.BaseWeb3Error{constructor(){super("Unsupported key store version"),this.code=s.ERR_KEY_VERSION_UNSUPPORTED}}t.KeyStoreVersionError=u;class l extends n.BaseWeb3Error{constructor(){super("Password cannot be empty"),this.code=s.ERR_INVALID_PASSWORD}}t.InvalidPasswordError=l;class _ extends n.BaseWeb3Error{constructor(){super("Initialization vector must be 16 bytes"),this.code=s.ERR_IV_LENGTH}}t.IVLengthError=_;class p extends n.BaseWeb3Error{constructor(){super("c > 1000, pbkdf2 is less secure with less iterations"),this.code=s.ERR_PBKDF2_ITERATIONS}}t.PBKDF2IterationsError=p},3789:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.RequestAlreadySentError=t.PendingRequestsOnReconnectingError=t.MaxAttemptsReachedOnReconnectingError=t.ConnectionCloseError=t.ConnectionNotOpenError=t.ConnectionTimeoutError=t.InvalidConnectionError=t.ConnectionError=void 0;const s=r(7639),n=r(3685);class o extends n.BaseWeb3Error{constructor(e,t){super(e),this.code=s.ERR_CONN,t&&(this.errorCode=t.code,this.errorReason=t.reason)}toJSON(){return Object.assign(Object.assign({},super.toJSON()),{errorCode:this.errorCode,errorReason:this.errorReason})}}t.ConnectionError=o,t.InvalidConnectionError=class extends o{constructor(e,t){super(`CONNECTION ERROR: Couldn't connect to node ${e}.`,t),this.host=e,this.code=s.ERR_CONN_INVALID}toJSON(){return Object.assign(Object.assign({},super.toJSON()),{host:this.host})}},t.ConnectionTimeoutError=class extends o{constructor(e){super(`CONNECTION TIMEOUT: timeout of ${e}ms achieved`),this.duration=e,this.code=s.ERR_CONN_TIMEOUT}toJSON(){return Object.assign(Object.assign({},super.toJSON()),{duration:this.duration})}},t.ConnectionNotOpenError=class extends o{constructor(e){super("Connection not open",e),this.code=s.ERR_CONN_NOT_OPEN}},t.ConnectionCloseError=class extends o{constructor(e){var t,r;super(`CONNECTION ERROR: The connection got closed with the close code ${null!==(t=null==e?void 0:e.code)&&void 0!==t?t:""} and the following reason string ${null!==(r=null==e?void 0:e.reason)&&void 0!==r?r:""}`,e),this.code=s.ERR_CONN_CLOSE}},t.MaxAttemptsReachedOnReconnectingError=class extends o{constructor(e){super(`Maximum number of reconnect attempts reached! (${e})`),this.code=s.ERR_CONN_MAX_ATTEMPTS}},t.PendingRequestsOnReconnectingError=class extends o{constructor(){super("CONNECTION ERROR: Provider started to reconnect before the response got received!"),this.code=s.ERR_CONN_PENDING_REQUESTS}},t.RequestAlreadySentError=class extends o{constructor(e){super(`Request already sent with following id: ${e}`),this.code=s.ERR_REQ_ALREADY_SENT}}},510:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ContractTransactionDataAndInputError=t.ContractExecutionError=t.Eip838ExecutionError=t.ContractInstantiationError=t.ContractNoFromAddressDefinedError=t.ContractNoAddressDefinedError=t.ContractMissingDeployDataError=t.ContractReservedEventError=t.ContractEventDoesNotExistError=t.ContractOnceRequiresCallbackError=t.ContractMissingABIError=t.ResolverMethodMissingError=t.Web3ContractError=void 0;const s=r(7639),n=r(3685);class o extends n.BaseWeb3Error{constructor(e,t){super(e),this.code=s.ERR_CONTRACT,this.receipt=t}}t.Web3ContractError=o;class a extends n.BaseWeb3Error{constructor(e,t){super(`The resolver at ${e} does not implement requested method: "${t}".`),this.address=e,this.name=t,this.code=s.ERR_CONTRACT_RESOLVER_MISSING}toJSON(){return Object.assign(Object.assign({},super.toJSON()),{address:this.address,name:this.name})}}t.ResolverMethodMissingError=a;class i extends n.BaseWeb3Error{constructor(){super("You must provide the json interface of the contract when instantiating a contract object."),this.code=s.ERR_CONTRACT_ABI_MISSING}}t.ContractMissingABIError=i;class d extends n.BaseWeb3Error{constructor(){super("Once requires a callback as the second parameter."),this.code=s.ERR_CONTRACT_REQUIRED_CALLBACK}}t.ContractOnceRequiresCallbackError=d;class c extends n.BaseWeb3Error{constructor(e){super(`Event "${e}" doesn't exist in this contract.`),this.eventName=e,this.code=s.ERR_CONTRACT_EVENT_NOT_EXISTS}toJSON(){return Object.assign(Object.assign({},super.toJSON()),{eventName:this.eventName})}}t.ContractEventDoesNotExistError=c;class u extends n.BaseWeb3Error{constructor(e){super(`Event "${e}" doesn't exist in this contract.`),this.type=e,this.code=s.ERR_CONTRACT_RESERVED_EVENT}toJSON(){return Object.assign(Object.assign({},super.toJSON()),{type:this.type})}}t.ContractReservedEventError=u;class l extends n.BaseWeb3Error{constructor(){super('No "data" specified in neither the given options, nor the default options.'),this.code=s.ERR_CONTRACT_MISSING_DEPLOY_DATA}}t.ContractMissingDeployDataError=l;class _ extends n.BaseWeb3Error{constructor(){super("This contract object doesn't have address set yet, please set an address first."),this.code=s.ERR_CONTRACT_MISSING_ADDRESS}}t.ContractNoAddressDefinedError=_;class p extends n.BaseWeb3Error{constructor(){super('No "from" address specified in neither the given options, nor the default options.'),this.code=s.ERR_CONTRACT_MISSING_FROM_ADDRESS}}t.ContractNoFromAddressDefinedError=p;class h extends n.BaseWeb3Error{constructor(){super(...arguments),this.code=s.ERR_CONTRACT_INSTANTIATION}}t.ContractInstantiationError=h;class E extends o{constructor(e){if(super(e.message||"Error"),this.name="name"in e&&e.name||this.constructor.name,this.stack="stack"in e&&e.stack||void 0,this.code=e.code,"object"==typeof e.data){let t;t="originalError"in e.data?e.data.originalError:e.data,this.data=t.data,this.cause=new E(t)}else this.data=e.data}setDecodedProperties(e,t,r){this.errorName=e,this.errorSignature=t,this.errorArgs=r}toJSON(){let e=Object.assign(Object.assign({},super.toJSON()),{data:this.data});return this.errorName&&(e=Object.assign(Object.assign({},e),{errorName:this.errorName,errorSignature:this.errorSignature,errorArgs:this.errorArgs})),e}}t.Eip838ExecutionError=E,t.ContractExecutionError=class extends o{constructor(e){super("Error happened while trying to execute a function inside a smart contract"),this.code=s.ERR_CONTRACT_EXECUTION_REVERTED,this.cause=new E(e)}};class R extends n.InvalidValueError{constructor(e){var t,r;super(`data: ${null!==(t=e.data)&&void 0!==t?t:"undefined"}, input: ${null!==(r=e.input)&&void 0!==r?r:"undefined"}`,'You can\'t have "data" and "input" as properties of a contract at the same time, please use either "data" or "input" instead.'),this.code=s.ERR_CONTRACT_TX_DATA_AND_INPUT}}t.ContractTransactionDataAndInputError=R},3628:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ConfigChainMismatchError=t.ConfigHardforkMismatchError=void 0;const s=r(3685),n=r(7639);class o extends s.BaseWeb3Error{constructor(e,t){super(`Web3Config hardfork doesnt match in defaultHardfork ${e} and common.hardfork ${t}`),this.code=n.ERR_CORE_HARDFORK_MISMATCH}}t.ConfigHardforkMismatchError=o;class a extends s.BaseWeb3Error{constructor(e,t){super(`Web3Config chain doesnt match in defaultHardfork ${e} and common.hardfork ${t}`),this.code=n.ERR_CORE_HARDFORK_MISMATCH}}t.ConfigChainMismatchError=a},1591:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ENSNetworkNotSyncedError=t.ENSUnsupportedNetworkError=t.ENSCheckInterfaceSupportError=void 0;const s=r(7639),n=r(3685);class o extends n.BaseWeb3Error{constructor(e){super(`ENS resolver check interface support error. "${e}"`),this.code=s.ERR_ENS_CHECK_INTERFACE_SUPPORT}}t.ENSCheckInterfaceSupportError=o;class a extends n.BaseWeb3Error{constructor(e){super(`ENS is not supported on network ${e}`),this.code=s.ERR_ENS_UNSUPPORTED_NETWORK}}t.ENSUnsupportedNetworkError=a;class i extends n.BaseWeb3Error{constructor(){super("Network not synced"),this.code=s.ERR_ENS_NETWORK_NOT_SYNCED}}t.ENSNetworkNotSyncedError=i},7297:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ExistingPluginNamespaceError=t.AbiError=t.OperationAbortError=t.OperationTimeoutError=t.MethodNotImplementedError=t.FormatterError=t.InvalidMethodParamsError=t.InvalidNumberOfParamsError=void 0;const s=r(7639),n=r(3685);class o extends n.BaseWeb3Error{constructor(e,t,r){super(`Invalid number of parameters for "${r}". Got "${e}" expected "${t}"!`),this.got=e,this.expected=t,this.method=r,this.code=s.ERR_PARAM}toJSON(){return Object.assign(Object.assign({},super.toJSON()),{got:this.got,expected:this.expected,method:this.method})}}t.InvalidNumberOfParamsError=o;class a extends n.BaseWeb3Error{constructor(e){super(`Invalid parameters passed. "${void 0!==e?e:""}"`),this.hint=e,this.code=s.ERR_INVALID_METHOD_PARAMS}toJSON(){return Object.assign(Object.assign({},super.toJSON()),{hint:this.hint})}}t.InvalidMethodParamsError=a;class i extends n.BaseWeb3Error{constructor(){super(...arguments),this.code=s.ERR_FORMATTERS}}t.FormatterError=i;class d extends n.BaseWeb3Error{constructor(){super("The method you're trying to call is not implemented."),this.code=s.ERR_METHOD_NOT_IMPLEMENTED}}t.MethodNotImplementedError=d;class c extends n.BaseWeb3Error{constructor(){super(...arguments),this.code=s.ERR_OPERATION_TIMEOUT}}t.OperationTimeoutError=c;class u extends n.BaseWeb3Error{constructor(){super(...arguments),this.code=s.ERR_OPERATION_ABORT}}t.OperationAbortError=u;class l extends n.BaseWeb3Error{constructor(e,t){super(e),this.code=s.ERR_ABI_ENCODING,this.props=null!=t?t:{}}}t.AbiError=l;class _ extends n.BaseWeb3Error{constructor(e){super(`A plugin with the namespace: ${e} has already been registered.`),this.code=s.ERR_EXISTING_PLUGIN_NAMESPACE}}t.ExistingPluginNamespaceError=_},7108:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Web3WSProviderError=t.SubscriptionError=t.InvalidClientError=t.InvalidProviderError=t.ProviderError=void 0;const s=r(7639),n=r(3685);class o extends n.BaseWeb3Error{constructor(){super(...arguments),this.code=s.ERR_PROVIDER}}t.ProviderError=o;class a extends n.BaseWeb3Error{constructor(e){super(`Provider with url "${e}" is not set or invalid`),this.clientUrl=e,this.code=s.ERR_INVALID_PROVIDER}}t.InvalidProviderError=a;class i extends n.BaseWeb3Error{constructor(e){super(`Client URL "${e}" is invalid.`),this.code=s.ERR_INVALID_CLIENT}}t.InvalidClientError=i;class d extends n.BaseWeb3Error{constructor(){super(...arguments),this.code=s.ERR_SUBSCRIPTION}}t.SubscriptionError=d;class c extends n.BaseWeb3Error{constructor(){super(...arguments),this.code=s.ERR_WS_PROVIDER}}t.Web3WSProviderError=c},9491:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.InvalidResponseError=t.ResponseError=void 0;const s=r(3685),n=r(7639),o=e=>(e=>!(Array.isArray(e)||"2.0"!==e.jsonrpc||!e||void 0!==e.result&&null!==e.result||!("error"in e)||"number"!=typeof e.id&&"string"!=typeof e.id))(e)?e.error.message:"";class a extends s.BaseWeb3Error{constructor(e,t,r){var a;let i;super(null!=t?t:`Returned error: ${Array.isArray(e)?e.map((e=>o(e))).join(","):o(e)}`),this.code=n.ERR_RESPONSE,t||(this.data=Array.isArray(e)?e.map((e=>{var t;return null===(t=e.error)||void 0===t?void 0:t.data})):null===(a=null==e?void 0:e.error)||void 0===a?void 0:a.data),this.request=r,"error"in e?i=e.error:e instanceof Array&&(i=e.filter((e=>e.error)).map((e=>e.error))),Array.isArray(i)&&i.length>0?this.cause=new s.MultipleErrors(i):this.cause=i}toJSON(){return Object.assign(Object.assign({},super.toJSON()),{data:this.data,request:this.request})}}t.ResponseError=a,t.InvalidResponseError=class extends a{constructor(e,t){let r;super(e,void 0,t),this.code=n.ERR_INVALID_RESPONSE,"error"in e?r=e.error:e instanceof Array&&(r=e.map((e=>e.error))),Array.isArray(r)?this.cause=new s.MultipleErrors(r):this.cause=r}}},4032:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.RpcErrorMessages=t.genericRpcErrorMessageTemplate=void 0;const s=r(7639);t.genericRpcErrorMessageTemplate="An Rpc error has occured with a code of *code*",t.RpcErrorMessages={[s.ERR_RPC_INVALID_JSON]:{message:"Parse error",description:"Invalid JSON"},[s.ERR_RPC_INVALID_REQUEST]:{message:"Invalid request",description:"JSON is not a valid request object\t"},[s.ERR_RPC_INVALID_METHOD]:{message:"Method not found",description:"Method does not exist\t"},[s.ERR_RPC_INVALID_PARAMS]:{message:"Invalid params",description:"Invalid method parameters"},[s.ERR_RPC_INTERNAL_ERROR]:{message:"Internal error",description:"Internal JSON-RPC error"},[s.ERR_RPC_INVALID_INPUT]:{message:"Invalid input",description:"Missing or invalid parameters"},[s.ERR_RPC_MISSING_RESOURCE]:{message:"Resource not found",description:"Requested resource not found"},[s.ERR_RPC_UNAVAILABLE_RESOURCE]:{message:"Resource unavailable",description:"Requested resource not available"},[s.ERR_RPC_TRANSACTION_REJECTED]:{message:"Transaction rejected",description:"Transaction creation failed"},[s.ERR_RPC_UNSUPPORTED_METHOD]:{message:"Method not supported",description:"Method is not implemented"},[s.ERR_RPC_LIMIT_EXCEEDED]:{message:"Limit exceeded",description:"Request exceeds defined limit"},[s.ERR_RPC_NOT_SUPPORTED]:{message:"JSON-RPC version not supported",description:"Version of JSON-RPC protocol is not supported"},[s.JSONRPC_ERR_REJECTED_REQUEST]:{name:"User Rejected Request",message:"The user rejected the request."},[s.JSONRPC_ERR_UNAUTHORIZED]:{name:"Unauthorized",message:"The requested method and/or account has not been authorized by the user."},[s.JSONRPC_ERR_UNSUPPORTED_METHOD]:{name:"Unsupported Method",message:"The Provider does not support the requested method."},[s.JSONRPC_ERR_DISCONNECTED]:{name:"Disconnected",message:"The Provider is disconnected from all chains."},[s.JSONRPC_ERR_CHAIN_DISCONNECTED]:{name:"Chain Disconnected",message:"The Provider is not connected to the requested chain."},"0-999":{name:"",message:"Not used."},1e3:{name:"Normal Closure",message:"The connection successfully completed the purpose for which it was created."},1001:{name:"Going Away",message:"The endpoint is going away, either because of a server failure or because the browser is navigating away from the page that opened the connection."},1002:{name:"Protocol error",message:"The endpoint is terminating the connection due to a protocol error."},1003:{name:"Unsupported Data",message:"The connection is being terminated because the endpoint received data of a type it cannot accept. (For example, a text-only endpoint received binary data.)"},1004:{name:"Reserved",message:"Reserved. A meaning might be defined in the future."},1005:{name:"No Status Rcvd",message:"Reserved. Indicates that no status code was provided even though one was expected."},1006:{name:"Abnormal Closure",message:"Reserved. Indicates that a connection was closed abnormally (that is, with no close frame being sent) when a status code is expected."},1007:{name:"Invalid frame payload data",message:"The endpoint is terminating the connection because a message was received that contained inconsistent data (e.g., non-UTF-8 data within a text message)."},1008:{name:"Policy Violation",message:"The endpoint is terminating the connection because it received a message that violates its policy. This is a generic status code, used when codes 1003 and 1009 are not suitable."},1009:{name:"Message Too Big",message:"The endpoint is terminating the connection because a data frame was received that is too large."},1010:{name:"Mandatory Ext.",message:"The client is terminating the connection because it expected the server to negotiate one or more extension, but the server didn't."},1011:{name:"Internal Error",message:"The server is terminating the connection because it encountered an unexpected condition that prevented it from fulfilling the request."},1012:{name:"Service Restart",message:"The server is terminating the connection because it is restarting."},1013:{name:"Try Again Later",message:"The server is terminating the connection due to a temporary condition, e.g. it is overloaded and is casting off some of its clients."},1014:{name:"Bad Gateway",message:"The server was acting as a gateway or proxy and received an invalid response from the upstream server. This is similar to 502 HTTP Status Code."},1015:{name:"TLS handshake",message:"Reserved. Indicates that the connection was closed due to a failure to perform a TLS handshake (e.g., the server certificate can't be verified)."},"1016-2999":{name:"",message:"For definition by future revisions of the WebSocket Protocol specification, and for definition by extension specifications."},"3000-3999":{name:"",message:"For use by libraries, frameworks, and applications. These status codes are registered directly with IANA. The interpretation of these codes is undefined by the WebSocket protocol."},"4000-4999":{name:"",message:"For private use, and thus can't be registered. Such codes can be used by prior agreements between WebSocket applications. The interpretation of these codes is undefined by the WebSocket protocol."}}},655:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.rpcErrorsMap=t.LimitExceededError=t.TransactionRejectedError=t.VersionNotSupportedError=t.ResourcesNotFoundError=t.ResourceUnavailableError=t.MethodNotSupported=t.InvalidInputError=t.InternalError=t.InvalidParamsError=t.MethodNotFoundError=t.InvalidRequestError=t.ParseError=t.EIP1193ProviderRpcError=t.RpcError=void 0;const s=r(3685),n=r(7639),o=r(4032);class a extends s.BaseWeb3Error{constructor(e,t){super(null!=t?t:o.genericRpcErrorMessageTemplate.replace("*code*",e.error.code.toString())),this.code=e.error.code,this.id=e.id,this.jsonrpc=e.jsonrpc,this.jsonRpcError=e.error}toJSON(){return Object.assign(Object.assign({},super.toJSON()),{error:this.jsonRpcError,id:this.id,jsonRpc:this.jsonrpc})}}t.RpcError=a;class i extends s.BaseWeb3Error{constructor(e,t){var r,s,n,a;if(e)if(null===(r=o.RpcErrorMessages[e])||void 0===r?void 0:r.message)super(o.RpcErrorMessages[e].message);else{const t=Object.keys(o.RpcErrorMessages).find((t=>"string"==typeof t&&e>=parseInt(t.split("-")[0],10)&&e<=parseInt(t.split("-")[1],10)));super(null!==(n=null===(s=o.RpcErrorMessages[null!=t?t:""])||void 0===s?void 0:s.message)&&void 0!==n?n:o.genericRpcErrorMessageTemplate.replace("*code*",null!==(a=null==e?void 0:e.toString())&&void 0!==a?a:'""'))}else super();this.code=e,this.data=t}}t.EIP1193ProviderRpcError=i;class d extends a{constructor(e){super(e,o.RpcErrorMessages[n.ERR_RPC_INVALID_JSON].message),this.code=n.ERR_RPC_INVALID_JSON}}t.ParseError=d;class c extends a{constructor(e){super(e,o.RpcErrorMessages[n.ERR_RPC_INVALID_REQUEST].message),this.code=n.ERR_RPC_INVALID_REQUEST}}t.InvalidRequestError=c;class u extends a{constructor(e){super(e,o.RpcErrorMessages[n.ERR_RPC_INVALID_METHOD].message),this.code=n.ERR_RPC_INVALID_METHOD}}t.MethodNotFoundError=u;class l extends a{constructor(e){super(e,o.RpcErrorMessages[n.ERR_RPC_INVALID_PARAMS].message),this.code=n.ERR_RPC_INVALID_PARAMS}}t.InvalidParamsError=l;class _ extends a{constructor(e){super(e,o.RpcErrorMessages[n.ERR_RPC_INTERNAL_ERROR].message),this.code=n.ERR_RPC_INTERNAL_ERROR}}t.InternalError=_;class p extends a{constructor(e){super(e,o.RpcErrorMessages[n.ERR_RPC_INVALID_INPUT].message),this.code=n.ERR_RPC_INVALID_INPUT}}t.InvalidInputError=p;class h extends a{constructor(e){super(e,o.RpcErrorMessages[n.ERR_RPC_UNSUPPORTED_METHOD].message),this.code=n.ERR_RPC_UNSUPPORTED_METHOD}}t.MethodNotSupported=h;class E extends a{constructor(e){super(e,o.RpcErrorMessages[n.ERR_RPC_UNAVAILABLE_RESOURCE].message),this.code=n.ERR_RPC_UNAVAILABLE_RESOURCE}}t.ResourceUnavailableError=E;class R extends a{constructor(e){super(e,o.RpcErrorMessages[n.ERR_RPC_MISSING_RESOURCE].message),this.code=n.ERR_RPC_MISSING_RESOURCE}}t.ResourcesNotFoundError=R;class f extends a{constructor(e){super(e,o.RpcErrorMessages[n.ERR_RPC_NOT_SUPPORTED].message),this.code=n.ERR_RPC_NOT_SUPPORTED}}t.VersionNotSupportedError=f;class m extends a{constructor(e){super(e,o.RpcErrorMessages[n.ERR_RPC_TRANSACTION_REJECTED].message),this.code=n.ERR_RPC_TRANSACTION_REJECTED}}t.TransactionRejectedError=m;class I extends a{constructor(e){super(e,o.RpcErrorMessages[n.ERR_RPC_LIMIT_EXCEEDED].message),this.code=n.ERR_RPC_LIMIT_EXCEEDED}}t.LimitExceededError=I,t.rpcErrorsMap=new Map,t.rpcErrorsMap.set(n.ERR_RPC_INVALID_JSON,{error:d}),t.rpcErrorsMap.set(n.ERR_RPC_INVALID_REQUEST,{error:c}),t.rpcErrorsMap.set(n.ERR_RPC_INVALID_METHOD,{error:u}),t.rpcErrorsMap.set(n.ERR_RPC_INVALID_PARAMS,{error:l}),t.rpcErrorsMap.set(n.ERR_RPC_INTERNAL_ERROR,{error:_}),t.rpcErrorsMap.set(n.ERR_RPC_INVALID_INPUT,{error:p}),t.rpcErrorsMap.set(n.ERR_RPC_UNSUPPORTED_METHOD,{error:h}),t.rpcErrorsMap.set(n.ERR_RPC_UNAVAILABLE_RESOURCE,{error:E}),t.rpcErrorsMap.set(n.ERR_RPC_TRANSACTION_REJECTED,{error:m}),t.rpcErrorsMap.set(n.ERR_RPC_MISSING_RESOURCE,{error:R}),t.rpcErrorsMap.set(n.ERR_RPC_NOT_SUPPORTED,{error:f}),t.rpcErrorsMap.set(n.ERR_RPC_LIMIT_EXCEEDED,{error:I})},1066:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.SchemaFormatError=void 0;const s=r(7639),n=r(3685);class o extends n.BaseWeb3Error{constructor(e){super(`Format for the type ${e} is unsupported`),this.type=e,this.code=s.ERR_SCHEMA_FORMAT}toJSON(){return Object.assign(Object.assign({},super.toJSON()),{type:this.type})}}t.SchemaFormatError=o},1075:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.SignatureError=void 0;const s=r(7639),n=r(3685);class o extends n.InvalidValueError{constructor(){super(...arguments),this.code=s.ERR_SIGNATURE_FAILED}}t.SignatureError=o},8450:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.InvalidPropertiesForTransactionTypeError=t.LocalWalletNotAvailableError=t.TransactionSigningError=t.TransactionReceiptMissingBlockNumberError=t.TransactionMissingReceiptOrBlockHashError=t.TransactionBlockTimeoutError=t.TransactionPollingTimeoutError=t.TransactionSendTimeoutError=t.TransactionDataAndInputError=t.UnsupportedTransactionTypeError=t.Eip1559NotSupportedError=t.UnableToPopulateNonceError=t.InvalidNonceOrChainIdError=t.InvalidTransactionObjectError=t.UnsupportedFeeMarketError=t.Eip1559GasPriceError=t.InvalidMaxPriorityFeePerGasOrMaxFeePerGas=t.InvalidGasOrGasPrice=t.TransactionGasMismatchError=t.TransactionGasMismatchInnerError=t.MissingGasError=t.MissingGasInnerError=t.MissingChainOrHardforkError=t.CommonOrChainAndHardforkError=t.HardforkMismatchError=t.ChainMismatchError=t.ChainIdMismatchError=t.MissingCustomChainIdError=t.MissingCustomChainError=t.InvalidTransactionCall=t.InvalidTransactionWithReceiver=t.InvalidTransactionWithSender=t.TransactionNotFound=t.UndefinedRawTransactionError=t.TransactionOutOfGasError=t.TransactionRevertedWithoutReasonError=t.ContractCodeNotStoredError=t.NoContractAddressFoundError=t.TransactionRevertWithCustomError=t.TransactionRevertInstructionError=t.RevertInstructionError=t.TransactionError=void 0;const s=r(7639),n=r(3685);class o extends n.BaseWeb3Error{constructor(e,t){super(e),this.receipt=t,this.code=s.ERR_TX}toJSON(){return Object.assign(Object.assign({},super.toJSON()),{receipt:this.receipt})}}t.TransactionError=o;class a extends n.BaseWeb3Error{constructor(e,t){super(`Your request got reverted with the following reason string: ${e}`),this.reason=e,this.signature=t,this.code=s.ERR_TX_REVERT_INSTRUCTION}toJSON(){return Object.assign(Object.assign({},super.toJSON()),{reason:this.reason,signature:this.signature})}}t.RevertInstructionError=a;class i extends n.BaseWeb3Error{constructor(e,t,r,o){super("Transaction has been reverted by the EVM"+(void 0===r?"":`:\n ${n.BaseWeb3Error.convertToString(r)}`)),this.reason=e,this.signature=t,this.receipt=r,this.data=o,this.code=s.ERR_TX_REVERT_TRANSACTION}toJSON(){return Object.assign(Object.assign({},super.toJSON()),{reason:this.reason,signature:this.signature,receipt:this.receipt,data:this.data})}}t.TransactionRevertInstructionError=i,t.TransactionRevertWithCustomError=class extends i{constructor(e,t,r,n,o,a,i){super(e),this.reason=e,this.customErrorName=t,this.customErrorDecodedSignature=r,this.customErrorArguments=n,this.signature=o,this.receipt=a,this.data=i,this.code=s.ERR_TX_REVERT_TRANSACTION_CUSTOM_ERROR}toJSON(){return Object.assign(Object.assign({},super.toJSON()),{reason:this.reason,customErrorName:this.customErrorName,customErrorDecodedSignature:this.customErrorDecodedSignature,customErrorArguments:this.customErrorArguments,signature:this.signature,receipt:this.receipt,data:this.data})}},t.NoContractAddressFoundError=class extends o{constructor(e){super("The transaction receipt didn't contain a contract address.",e),this.code=s.ERR_TX_NO_CONTRACT_ADDRESS}toJSON(){return Object.assign(Object.assign({},super.toJSON()),{receipt:this.receipt})}},t.ContractCodeNotStoredError=class extends o{constructor(e){super("The contract code couldn't be stored, please check your gas limit.",e),this.code=s.ERR_TX_CONTRACT_NOT_STORED}},t.TransactionRevertedWithoutReasonError=class extends o{constructor(e){super("Transaction has been reverted by the EVM"+(void 0===e?"":`:\n ${n.BaseWeb3Error.convertToString(e)}`),e),this.code=s.ERR_TX_REVERT_WITHOUT_REASON}},t.TransactionOutOfGasError=class extends o{constructor(e){super(`Transaction ran out of gas. Please provide more gas:\n ${JSON.stringify(e,void 0,2)}`,e),this.code=s.ERR_TX_OUT_OF_GAS}},t.UndefinedRawTransactionError=class extends o{constructor(){super("Raw transaction undefined"),this.code=s.ERR_RAW_TX_UNDEFINED}},t.TransactionNotFound=class extends o{constructor(){super("Transaction not found"),this.code=s.ERR_TX_NOT_FOUND}};class d extends n.InvalidValueError{constructor(e){super(e,"invalid transaction with invalid sender"),this.code=s.ERR_TX_INVALID_SENDER}}t.InvalidTransactionWithSender=d;class c extends n.InvalidValueError{constructor(e){super(e,"invalid transaction with invalid receiver"),this.code=s.ERR_TX_INVALID_RECEIVER}}t.InvalidTransactionWithReceiver=c;class u extends n.InvalidValueError{constructor(e){super(e,"invalid transaction call"),this.code=s.ERR_TX_INVALID_CALL}}t.InvalidTransactionCall=u;class l extends n.InvalidValueError{constructor(){super("MissingCustomChainError","If tx.common is provided it must have tx.common.customChain"),this.code=s.ERR_TX_MISSING_CUSTOM_CHAIN}}t.MissingCustomChainError=l;class _ extends n.InvalidValueError{constructor(){super("MissingCustomChainIdError","If tx.common is provided it must have tx.common.customChain and tx.common.customChain.chainId"),this.code=s.ERR_TX_MISSING_CUSTOM_CHAIN_ID}}t.MissingCustomChainIdError=_;class p extends n.InvalidValueError{constructor(e){super(JSON.stringify(e),"Chain Id doesnt match in tx.chainId tx.common.customChain.chainId"),this.code=s.ERR_TX_CHAIN_ID_MISMATCH}}t.ChainIdMismatchError=p;class h extends n.InvalidValueError{constructor(e){super(JSON.stringify(e),"Chain doesnt match in tx.chain tx.common.basechain"),this.code=s.ERR_TX_CHAIN_MISMATCH}}t.ChainMismatchError=h;class E extends n.InvalidValueError{constructor(e){super(JSON.stringify(e),"hardfork doesnt match in tx.hardfork tx.common.hardfork"),this.code=s.ERR_TX_HARDFORK_MISMATCH}}t.HardforkMismatchError=E;class R extends n.InvalidValueError{constructor(){super("CommonOrChainAndHardforkError","Please provide the common object or the chain and hardfork property but not all together."),this.code=s.ERR_TX_INVALID_CHAIN_INFO}}t.CommonOrChainAndHardforkError=R;class f extends n.InvalidValueError{constructor(e){var t,r;super("MissingChainOrHardforkError",`When specifying chain and hardfork, both values must be defined. Received "chain": ${null!==(t=e.chain)&&void 0!==t?t:"undefined"}, "hardfork": ${null!==(r=e.hardfork)&&void 0!==r?r:"undefined"}`),this.code=s.ERR_TX_MISSING_CHAIN_INFO}}t.MissingChainOrHardforkError=f;class m extends n.BaseWeb3Error{constructor(){super('Missing properties in transaction, either define "gas" and "gasPrice" for type 0 transactions or "gas", "maxPriorityFeePerGas" and "maxFeePerGas" for type 2 transactions'),this.code=s.ERR_TX_MISSING_GAS_INNER_ERROR}}t.MissingGasInnerError=m;class I extends n.InvalidValueError{constructor(e){var t,r,n,o;super(`gas: ${null!==(t=e.gas)&&void 0!==t?t:"undefined"}, gasPrice: ${null!==(r=e.gasPrice)&&void 0!==r?r:"undefined"}, maxPriorityFeePerGas: ${null!==(n=e.maxPriorityFeePerGas)&&void 0!==n?n:"undefined"}, maxFeePerGas: ${null!==(o=e.maxFeePerGas)&&void 0!==o?o:"undefined"}`,'"gas" is missing'),this.code=s.ERR_TX_MISSING_GAS,this.cause=new m}}t.MissingGasError=I;class y extends n.BaseWeb3Error{constructor(){super('Missing properties in transaction, either define "gas" and "gasPrice" for type 0 transactions or "gas", "maxPriorityFeePerGas" and "maxFeePerGas" for type 2 transactions, not both'),this.code=s.ERR_TX_GAS_MISMATCH_INNER_ERROR}}t.TransactionGasMismatchInnerError=y;class T extends n.InvalidValueError{constructor(e){var t,r,n,o;super(`gas: ${null!==(t=e.gas)&&void 0!==t?t:"undefined"}, gasPrice: ${null!==(r=e.gasPrice)&&void 0!==r?r:"undefined"}, maxPriorityFeePerGas: ${null!==(n=e.maxPriorityFeePerGas)&&void 0!==n?n:"undefined"}, maxFeePerGas: ${null!==(o=e.maxFeePerGas)&&void 0!==o?o:"undefined"}`,"transaction must specify legacy or fee market gas properties, not both"),this.code=s.ERR_TX_GAS_MISMATCH,this.cause=new y}}t.TransactionGasMismatchError=T;class v extends n.InvalidValueError{constructor(e){var t,r;super(`gas: ${null!==(t=e.gas)&&void 0!==t?t:"undefined"}, gasPrice: ${null!==(r=e.gasPrice)&&void 0!==r?r:"undefined"}`,"Gas or gasPrice is lower than 0"),this.code=s.ERR_TX_INVALID_LEGACY_GAS}}t.InvalidGasOrGasPrice=v;class g extends n.InvalidValueError{constructor(e){var t,r;super(`maxPriorityFeePerGas: ${null!==(t=e.maxPriorityFeePerGas)&&void 0!==t?t:"undefined"}, maxFeePerGas: ${null!==(r=e.maxFeePerGas)&&void 0!==r?r:"undefined"}`,"maxPriorityFeePerGas or maxFeePerGas is lower than 0"),this.code=s.ERR_TX_INVALID_FEE_MARKET_GAS}}t.InvalidMaxPriorityFeePerGasOrMaxFeePerGas=g;class N extends n.InvalidValueError{constructor(e){super(e,"eip-1559 transactions don't support gasPrice"),this.code=s.ERR_TX_INVALID_FEE_MARKET_GAS_PRICE}}t.Eip1559GasPriceError=N;class b extends n.InvalidValueError{constructor(e){var t,r;super(`maxPriorityFeePerGas: ${null!==(t=e.maxPriorityFeePerGas)&&void 0!==t?t:"undefined"}, maxFeePerGas: ${null!==(r=e.maxFeePerGas)&&void 0!==r?r:"undefined"}`,"pre-eip-1559 transaction don't support maxFeePerGas/maxPriorityFeePerGas"),this.code=s.ERR_TX_INVALID_LEGACY_FEE_MARKET}}t.UnsupportedFeeMarketError=b;class O extends n.InvalidValueError{constructor(e){super(e,"invalid transaction object"),this.code=s.ERR_TX_INVALID_OBJECT}}t.InvalidTransactionObjectError=O;class A extends n.InvalidValueError{constructor(e){var t,r;super(`nonce: ${null!==(t=e.nonce)&&void 0!==t?t:"undefined"}, chainId: ${null!==(r=e.chainId)&&void 0!==r?r:"undefined"}`,"Nonce or chainId is lower than 0"),this.code=s.ERR_TX_INVALID_NONCE_OR_CHAIN_ID}}t.InvalidNonceOrChainIdError=A;class C extends n.InvalidValueError{constructor(){super("UnableToPopulateNonceError","unable to populate nonce, no from address available"),this.code=s.ERR_TX_UNABLE_TO_POPULATE_NONCE}}t.UnableToPopulateNonceError=C;class S extends n.InvalidValueError{constructor(){super("Eip1559NotSupportedError","Network doesn't support eip-1559"),this.code=s.ERR_TX_UNSUPPORTED_EIP_1559}}t.Eip1559NotSupportedError=S;class x extends n.InvalidValueError{constructor(e){super(e,"unsupported transaction type"),this.code=s.ERR_TX_UNSUPPORTED_TYPE}}t.UnsupportedTransactionTypeError=x;class P extends n.InvalidValueError{constructor(e){var t,r;super(`data: ${null!==(t=e.data)&&void 0!==t?t:"undefined"}, input: ${null!==(r=e.input)&&void 0!==r?r:"undefined"}`,'You can\'t have "data" and "input" as properties of transactions at the same time, please use either "data" or "input" instead.'),this.code=s.ERR_TX_DATA_AND_INPUT}}t.TransactionDataAndInputError=P;class D extends n.BaseWeb3Error{constructor(e){super(`The connected Ethereum Node did not respond within ${e.numberOfSeconds} seconds, please make sure your transaction was properly sent and you are connected to a healthy Node. Be aware that transaction might still be pending or mined!\n\tTransaction Hash: ${e.transactionHash?e.transactionHash.toString():"not available"}`),this.code=s.ERR_TX_SEND_TIMEOUT}}function M(e){return`Please make sure your transaction was properly sent and there are no previous pending transaction for the same account. However, be aware that it might still be mined!\n\tTransaction Hash: ${e?e.toString():"not available"}`}t.TransactionSendTimeoutError=D;class k extends n.BaseWeb3Error{constructor(e){super(`Transaction was not mined within ${e.numberOfSeconds} seconds. ${M(e.transactionHash)}`),this.code=s.ERR_TX_POLLING_TIMEOUT}}t.TransactionPollingTimeoutError=k;class L extends n.BaseWeb3Error{constructor(e){super(`Transaction started at ${e.starterBlockNumber} but was not mined within ${e.numberOfBlocks} blocks. ${M(e.transactionHash)}`),this.code=s.ERR_TX_BLOCK_TIMEOUT}}t.TransactionBlockTimeoutError=L;class w extends n.InvalidValueError{constructor(e){var t,r;super(`receipt: ${JSON.stringify(e.receipt)}, blockHash: ${null===(t=e.blockHash)||void 0===t?void 0:t.toString()}, transactionHash: ${null===(r=e.transactionHash)||void 0===r?void 0:r.toString()}`,"Receipt missing or blockHash null"),this.code=s.ERR_TX_RECEIPT_MISSING_OR_BLOCKHASH_NULL}}t.TransactionMissingReceiptOrBlockHashError=w;class Z extends n.InvalidValueError{constructor(e){super(`receipt: ${JSON.stringify(e.receipt)}`,"Receipt missing block number"),this.code=s.ERR_TX_RECEIPT_MISSING_BLOCK_NUMBER}}t.TransactionReceiptMissingBlockNumberError=Z;class U extends n.BaseWeb3Error{constructor(e){super(`Invalid signature. "${e}"`),this.code=s.ERR_TX_SIGNING}}t.TransactionSigningError=U;class V extends n.InvalidValueError{constructor(){super("LocalWalletNotAvailableError","Attempted to index account in local wallet, but no wallet is available"),this.code=s.ERR_TX_LOCAL_WALLET_NOT_AVAILABLE}}t.LocalWalletNotAvailableError=V;class B extends n.BaseWeb3Error{constructor(e,t){const r=[];e.forEach((e=>r.push(e.keyword))),super(`The following properties are invalid for the transaction type ${t}: ${r.join(", ")}`),this.code=s.ERR_TX_INVALID_PROPERTIES_FOR_TYPE}}t.InvalidPropertiesForTransactionTypeError=B},4618:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.InvalidTypeAbiInputError=t.InvalidBlockError=t.InvalidLargeValueError=t.InvalidSizeError=t.InvalidUnsignedIntegerError=t.InvalidBooleanError=t.InvalidTypeError=t.NibbleWidthError=t.HexProcessingError=t.InvalidIntegerError=t.InvalidUnitError=t.InvalidStringError=t.InvalidAddressError=t.InvalidNumberError=t.InvalidBytesError=void 0;const s=r(7639),n=r(3685);class o extends n.InvalidValueError{constructor(e){super(e,"can not parse as byte data"),this.code=s.ERR_INVALID_BYTES}}t.InvalidBytesError=o;class a extends n.InvalidValueError{constructor(e){super(e,"can not parse as number data"),this.code=s.ERR_INVALID_NUMBER}}t.InvalidNumberError=a;class i extends n.InvalidValueError{constructor(e){super(e,"invalid ethereum address"),this.code=s.ERR_INVALID_ADDRESS}}t.InvalidAddressError=i;class d extends n.InvalidValueError{constructor(e){super(e,"not a valid string"),this.code=s.ERR_INVALID_STRING}}t.InvalidStringError=d;class c extends n.InvalidValueError{constructor(e){super(e,"invalid unit"),this.code=s.ERR_INVALID_UNIT}}t.InvalidUnitError=c;class u extends n.InvalidValueError{constructor(e){super(e,"not a valid unit. Must be a positive integer"),this.code=s.ERR_INVALID_INTEGER}}t.InvalidIntegerError=u;class l extends n.InvalidValueError{constructor(e){super(e,"can not be converted to hex"),this.code=s.ERR_INVALID_HEX}}t.HexProcessingError=l;class _ extends n.InvalidValueError{constructor(e){super(e,"value greater than the nibble width"),this.code=s.ERR_INVALID_NIBBLE_WIDTH}}t.NibbleWidthError=_;class p extends n.InvalidValueError{constructor(e){super(e,"invalid type, type not supported"),this.code=s.ERR_INVALID_TYPE}}t.InvalidTypeError=p;class h extends n.InvalidValueError{constructor(e){super(e,"not a valid boolean."),this.code=s.ERR_INVALID_BOOLEAN}}t.InvalidBooleanError=h;class E extends n.InvalidValueError{constructor(e){super(e,"not a valid unsigned integer."),this.code=s.ERR_INVALID_UNSIGNED_INTEGER}}t.InvalidUnsignedIntegerError=E;class R extends n.InvalidValueError{constructor(e){super(e,"invalid size given."),this.code=s.ERR_INVALID_SIZE}}t.InvalidSizeError=R;class f extends n.InvalidValueError{constructor(e){super(e,"value is larger than size."),this.code=s.ERR_INVALID_LARGE_VALUE}}t.InvalidLargeValueError=f;class m extends n.InvalidValueError{constructor(e){super(e,"invalid string given"),this.code=s.ERR_INVALID_BLOCK}}t.InvalidBlockError=m;class I extends n.InvalidValueError{constructor(e){super(e,"components found but type is not tuple"),this.code=s.ERR_INVALID_TYPE_ABI}}t.InvalidTypeAbiInputError=I},5071:function(e,t,r){var s=this&&this.__createBinding||(Object.create?function(e,t,r,s){void 0===s&&(s=r);var n=Object.getOwnPropertyDescriptor(t,r);n&&!("get"in n?!t.__esModule:n.writable||n.configurable)||(n={enumerable:!0,get:function(){return t[r]}}),Object.defineProperty(e,s,n)}:function(e,t,r,s){void 0===s&&(s=r),e[s]=t[r]}),n=this&&this.__exportStar||function(e,t){for(var r in e)"default"===r||Object.prototype.hasOwnProperty.call(t,r)||s(t,e,r)};Object.defineProperty(t,"__esModule",{value:!0}),n(r(7639),t),n(r(3685),t),n(r(8105),t),n(r(3789),t),n(r(510),t),n(r(1591),t),n(r(7297),t),n(r(7108),t),n(r(1075),t),n(r(8450),t),n(r(4618),t),n(r(9491),t),n(r(3628),t),n(r(655),t),n(r(4032),t),n(r(1066),t)},3685:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.InvalidValueError=t.MultipleErrors=t.BaseWeb3Error=void 0;const s=r(7639);class n extends Error{constructor(e,t){super(e),Array.isArray(t)?this.cause=new o(t):this.cause=t,this.name=this.constructor.name,"function"==typeof Error.captureStackTrace?Error.captureStackTrace(new.target.constructor):this.stack=(new Error).stack}get innerError(){return this.cause instanceof o?this.cause.errors:this.cause}set innerError(e){Array.isArray(e)?this.cause=new o(e):this.cause=e}static convertToString(e,t=!1){if(null==e)return"undefined";const r=JSON.stringify(e,((e,t)=>"bigint"==typeof t?t.toString():t));return t&&["bigint","string"].includes(typeof e)?r.replace(/['\\"]+/g,""):r}toJSON(){return{name:this.name,code:this.code,message:this.message,cause:this.cause,innerError:this.cause}}}t.BaseWeb3Error=n;class o extends n{constructor(e){super(`Multiple errors occurred: [${e.map((e=>e.message)).join("], [")}]`),this.code=s.ERR_MULTIPLE_ERRORS,this.errors=e}}t.MultipleErrors=o,t.InvalidValueError=class extends n{constructor(e,t){super(`Invalid value given "${n.convertToString(e,!0)}". Error: ${t}.`),this.name=this.constructor.name}}},6325:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0})},5529:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0})},2453:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0})},2856:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0})},6877:(e,t)=>{var r,s;Object.defineProperty(t,"__esModule",{value:!0}),t.ETH_DATA_FORMAT=t.DEFAULT_RETURN_FORMAT=t.FMT_BYTES=t.FMT_NUMBER=void 0,function(e){e.NUMBER="NUMBER_NUMBER",e.HEX="NUMBER_HEX",e.STR="NUMBER_STR",e.BIGINT="NUMBER_BIGINT"}(r=t.FMT_NUMBER||(t.FMT_NUMBER={})),function(e){e.HEX="BYTES_HEX",e.UINT8ARRAY="BYTES_UINT8ARRAY"}(s=t.FMT_BYTES||(t.FMT_BYTES={})),t.DEFAULT_RETURN_FORMAT={number:r.BIGINT,bytes:s.HEX},t.ETH_DATA_FORMAT={number:r.HEX,bytes:s.HEX}},9779:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0})},1517:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0})},8223:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0})},4205:(e,t)=>{var r,s;Object.defineProperty(t,"__esModule",{value:!0}),t.HardforksOrdered=t.BlockTags=void 0,(s=t.BlockTags||(t.BlockTags={})).EARLIEST="earliest",s.LATEST="latest",s.PENDING="pending",s.SAFE="safe",s.FINALIZED="finalized",(r=t.HardforksOrdered||(t.HardforksOrdered={})).chainstart="chainstart",r.frontier="frontier",r.homestead="homestead",r.dao="dao",r.tangerineWhistle="tangerineWhistle",r.spuriousDragon="spuriousDragon",r.byzantium="byzantium",r.constantinople="constantinople",r.petersburg="petersburg",r.istanbul="istanbul",r.muirGlacier="muirGlacier",r.berlin="berlin",r.london="london",r.altair="altair",r.arrowGlacier="arrowGlacier",r.grayGlacier="grayGlacier",r.bellatrix="bellatrix",r.merge="merge",r.capella="capella",r.shanghai="shanghai"},9970:function(e,t,r){var s=this&&this.__createBinding||(Object.create?function(e,t,r,s){void 0===s&&(s=r);var n=Object.getOwnPropertyDescriptor(t,r);n&&!("get"in n?!t.__esModule:n.writable||n.configurable)||(n={enumerable:!0,get:function(){return t[r]}}),Object.defineProperty(e,s,n)}:function(e,t,r,s){void 0===s&&(s=r),e[s]=t[r]}),n=this&&this.__exportStar||function(e,t){for(var r in e)"default"===r||Object.prototype.hasOwnProperty.call(t,r)||s(t,e,r)};Object.defineProperty(t,"__esModule",{value:!0}),n(r(9779),t),n(r(6325),t),n(r(2453),t),n(r(2856),t),n(r(5529),t),n(r(6877),t),n(r(4205),t),n(r(1517),t),n(r(8223),t),n(r(2196),t),n(r(8887),t),n(r(8173),t),n(r(1040),t),n(r(5640),t),n(r(1436),t),n(r(4933),t)},2196:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0})},8887:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TypedArray=void 0,t.TypedArray=Object.getPrototypeOf(Uint8Array)},8173:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0})},1040:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0})},5640:function(e,t){var r=this&&this.__awaiter||function(e,t,r,s){return new(r||(r=Promise))((function(n,o){function a(e){try{d(s.next(e))}catch(e){o(e)}}function i(e){try{d(s.throw(e))}catch(e){o(e)}}function d(e){var t;e.done?n(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(a,i)}d((s=s.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0}),t.Web3BaseProvider=void 0;const s=Symbol.for("web3/base-provider");class n{static isWeb3Provider(e){return e instanceof n||Boolean(e&&e[s])}get[s](){return!0}send(e,t){this.request(e).then((e=>{t(null,e)})).catch((e=>{t(e)}))}sendAsync(e){return r(this,void 0,void 0,(function*(){return this.request(e)}))}asEIP1193Provider(){const e=Object.create(this),t=e.request;return e.request=function(e){return r(this,void 0,void 0,(function*(){return(yield t(e)).result}))},e.asEIP1193Provider=void 0,e}}t.Web3BaseProvider=n},1436:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Web3BaseWallet=void 0,t.Web3BaseWallet=class extends Array{constructor(e){super(),this._accountProvider=e}}},4933:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0})}},t={};function r(s){var n=t[s];if(void 0!==n)return n.exports;var o=t[s]={id:s,loaded:!1,exports:{}};return e[s].call(o.exports,o,o.exports,r),o.loaded=!0,o.exports}r.nmd=e=>(e.paths=[],e.children||(e.children=[]),e);var s=r(6097);return s.default})()));
//# sourceMappingURL=web3-validator.min.js.map