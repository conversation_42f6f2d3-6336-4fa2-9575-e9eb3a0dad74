#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
批量获取Gate.io低交易量代币在各DEX上的24小时交易量数据

使用Gate.io低交易量代币数据和GeckoTerminal API检查DEX上的交易量情况
支持的网络映射：
- ETH → ethereum
- BSC → bnb
- MATIC → polygon_pos
- BASEEVM → base
- BLASTETH → blast
"""

import os
import json
import time
import asyncio
import aiohttp
import logging
import platform
import argparse
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from collections import defaultdict

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(f"gate_dex_vol_checker_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
    ]
)
logger = logging.getLogger(__name__)

# 常量配置
GATE_DATA_FILE = "data/cex/gate_info/gate_low_volume_usdt_pairs_with_network.json"
OUTPUT_DIR = "data/cex/gate_info/dex_vol"
API_RATE_LIMIT = 30  # 每个批次可以查询的最大代币数
API_DELAY = 1.1  # API调用之间的延迟时间（秒）
MAX_RETRIES = 3  # API调用重试次数
MIN_VOLUME = 8  # 最小DEX交易量(USD)
MAX_VOLUME = 18888  # 最大DEX交易量(USD)

# 网络映射
NETWORK_MAPPING = {
    "ETH": "eth",
    "BSC": "bsc",
    "MATIC": "polygon_pos",
    "BASEEVM": "base"
}

# 支持的网络
SUPPORTED_NETWORKS = list(NETWORK_MAPPING.keys())


async def fetch_token_volumes(session: aiohttp.ClientSession, 
                             network: str, 
                             token_addresses: List[str]) -> Tuple[str, List[Dict]]:
    """
    从GeckoTerminal API获取特定网络上的代币交易量数据
    
    Args:
        session: aiohttp客户端会话
        network: 网络标识符
        token_addresses: 代币合约地址列表 (最多30个)
    
    Returns:
        包含网络和代币交易量数据的元组
    """
    if not token_addresses:
        return network, []
        
    # 确保每次不超过30个地址
    addresses_batch = token_addresses[:API_RATE_LIMIT]
    addresses_str = ",".join(addresses_batch)
    
    gecko_network = NETWORK_MAPPING.get(network)
    if not gecko_network:
        logger.warning(f"不支持的网络类型: {network}")
        return network, []
    
    # 使用API v2
    url = f"https://api.geckoterminal.com/api/v2/networks/{gecko_network}/tokens/multi/{addresses_str}"
    
    logger.info(f"网络映射: {network} -> {gecko_network}")
    logger.info(f"构建请求URL: {url}")
    
    for retry in range(MAX_RETRIES):
        try:
            logger.info(f"正在获取网络 {network} 的 {len(addresses_batch)} 个代币数据...")
            # 添加请求头
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36',
                'Accept': 'application/json'
            }
            async with session.get(url, headers=headers, timeout=30) as response:
                if response.status == 200:
                    data = await response.json()
                    tokens_data = data.get("data", [])
                    
                    # 打印返回的JSON数据，用于调试
                    if tokens_data:
                        sample_token = tokens_data[0]
                        logger.info(f"样本代币数据: ID={sample_token.get('id', '')}")
                        
                        # 查看volume_usd的格式
                        if "attributes" in sample_token:
                            volume_data = sample_token["attributes"].get("volume_usd", {})
                            logger.info(f"交易量数据格式: {type(volume_data)}, 内容: {volume_data}")
                    
                    logger.info(f"成功获取 {network} 网络上 {len(tokens_data)} 个代币数据")
                    return network, tokens_data
                else:
                    error_text = await response.text()
                    logger.error(f"API请求失败 {response.status}: {error_text}")
                    # 尝试请求单个网络的API端点测试连接
                    test_url = f"https://api.geckoterminal.com/api/v2/networks/{gecko_network}"
                    logger.info(f"测试基础网络API连接: {test_url}")
                    try:
                        async with session.get(test_url, headers=headers, timeout=10) as test_response:
                            test_result = await test_response.text()
                            logger.info(f"网络API测试结果 ({test_response.status}): {test_result[:200]}...")
                    except Exception as e:
                        logger.error(f"测试API连接异常: {str(e)}")
                    
                    if retry < MAX_RETRIES - 1:
                        wait_time = (retry + 1) * 2
                        logger.info(f"等待 {wait_time} 秒后重试...")
                        await asyncio.sleep(wait_time)
                    else:
                        return network, []
        except Exception as e:
            logger.error(f"请求异常: {str(e)}")
            if retry < MAX_RETRIES - 1:
                wait_time = (retry + 1) * 2
                logger.info(f"等待 {wait_time} 秒后重试...")
                await asyncio.sleep(wait_time)
            else:
                return network, []
    
    return network, []


async def process_tokens_by_network(tokens_by_network: Dict[str, List[Dict]]) -> Dict[str, List[Dict]]:
    """
    处理每个网络的代币列表，并通过API获取交易量数据
    
    Args:
        tokens_by_network: 按网络分组的代币列表
        
    Returns:
        包含DEX交易量数据的字典
    """
    results = {}
    
    async with aiohttp.ClientSession() as session:
        for network, tokens in tokens_by_network.items():
            if network not in SUPPORTED_NETWORKS:
                logger.info(f"跳过不支持的网络: {network}")
                continue
                
            logger.info(f"处理网络 {network} 的 {len(tokens)} 个代币...")
            
            # 提取合约地址
            token_addresses = []
            for token in tokens:
                address = token.get("contract_address")
                if address and address.startswith("0x"):
                    token_addresses.append(address.lower())
            
            # 按批次处理代币地址
            all_token_data = []
            for i in range(0, len(token_addresses), API_RATE_LIMIT):
                batch = token_addresses[i:i+API_RATE_LIMIT]
                logger.info(f"处理网络 {network} 的批次 {i//API_RATE_LIMIT + 1}/{(len(token_addresses)-1)//API_RATE_LIMIT + 1} ({len(batch)} 个代币)")
                
                network_name, token_data = await fetch_token_volumes(session, network, batch)
                if token_data:
                    all_token_data.extend(token_data)
                
                # 添加延迟以遵守API速率限制
                await asyncio.sleep(API_DELAY)
            
            results[network] = all_token_data
    
    return results


def load_gate_tokens() -> Dict[str, List[Dict]]:
    """
    加载Gate.io的低交易量代币数据
    
    Returns:
        按网络分组的代币字典
    """
    try:
        with open(GATE_DATA_FILE, 'r', encoding='utf-8') as f:
            data = json.load(f)
            return data.get("networks", {})
    except Exception as e:
        logger.error(f"加载Gate.io数据失败: {str(e)}")
        return {}


def merge_data(gate_tokens: Dict[str, List[Dict]], dex_data: Dict[str, List[Dict]], min_volume: float = 8, max_volume: float = 18888) -> Dict:
    """
    合并Gate.io和DEX数据
    
    Args:
        gate_tokens: Gate.io代币数据
        dex_data: DEX交易量数据
        min_volume: 最小DEX交易量(USD)
        max_volume: 最大DEX交易量(USD)
        
    Returns:
        合并后的数据，只包含DEX交易量在指定范围内的代币
    """
    result = {
        "metadata": {
            "timestamp": int(time.time()),
            "datetime": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            "networks_processed": list(dex_data.keys()),
            "total_tokens_processed": sum(len(tokens) for tokens in gate_tokens.values() if tokens),
            "volume_range": {
                "min": min_volume,
                "max": max_volume
            }
        },
        "tokens_by_network": {}
    }
    
    # 创建DEX数据的地址索引
    dex_tokens_by_address = {}
    for network, tokens in dex_data.items():
        network_index = {}
        for token in tokens:
            token_id = token.get("id", "")
            if token_id and "_" in token_id:
                # 修正从token_id中提取address的方式，使用下划线分割
                address = token_id.split("_")[-1].lower()
                network_index[address] = token
        dex_tokens_by_address[network] = network_index
    
    # 预处理：首先收集所有代币符号及其交易量数据
    all_symbols_volumes = defaultdict(list)
    
    for network, tokens in gate_tokens.items():
        if network not in SUPPORTED_NETWORKS:
            continue
            
        for gate_token in tokens:
            contract_address = gate_token.get("contract_address", "").lower()
            symbol = gate_token.get("symbol")
            
            if not contract_address or not symbol:
                continue
                
            dex_token_data = dex_tokens_by_address.get(network, {}).get(contract_address)
            
            if dex_token_data:
                attributes = dex_token_data.get("attributes", {})
                volume_data = attributes.get("volume_usd", {})
                h24_volume = "0"
                
                # 处理不同格式的交易量数据
                if isinstance(volume_data, dict) and "h24" in volume_data:
                    h24_volume = volume_data.get("h24", "0")
                elif isinstance(volume_data, str):
                    h24_volume = volume_data
                
                try:
                    volume_float = float(h24_volume)
                    if volume_float > 0:  # 只记录大于0的交易量
                        # 记录该符号的交易量数据
                        all_symbols_volumes[symbol].append((volume_float, network))
                except (ValueError, TypeError):
                    logger.warning(f"无法转换交易量为浮点数: {h24_volume} (代币: {symbol})")
    
    # 确定哪些符号的代币在所有网络上的交易量都在min_volume-max_volume之间
    valid_symbols = set()
    for symbol, volumes_info in all_symbols_volumes.items():
        volumes = [v[0] for v in volumes_info]
        networks = [v[1] for v in volumes_info]
        # 只有当所有网络上的交易量都在指定范围内时，才认为是有效的
        if volumes and all(min_volume <= vol <= max_volume for vol in volumes):
            valid_symbols.add(symbol)
            logger.info(f"代币 {symbol} 符合交易量范围要求 ({min_volume}-{max_volume}): {list(zip(networks, volumes))}")
        else:
            logger.info(f"代币 {symbol} 不符合交易量范围要求 ({min_volume}-{max_volume}): {list(zip(networks, volumes))}")
    
    # 合并数据，只包含符合条件的代币，并只保存交易量在指定范围内的代币
    for network, tokens in gate_tokens.items():
        if network not in SUPPORTED_NETWORKS:
            continue
            
        merged_tokens = []
        for gate_token in tokens:
            contract_address = gate_token.get("contract_address", "").lower()
            symbol = gate_token.get("symbol")
            
            if not contract_address or not symbol or symbol not in valid_symbols:
                continue
                
            dex_token_data = dex_tokens_by_address.get(network, {}).get(contract_address)
            if not dex_token_data:
                continue
                
            attributes = dex_token_data.get("attributes", {})
            volume_data = attributes.get("volume_usd", {})
            h24_volume = "0"
            
            # 处理不同格式的交易量数据
            if isinstance(volume_data, dict) and "h24" in volume_data:
                h24_volume = volume_data.get("h24", "0")
            elif isinstance(volume_data, str):
                h24_volume = volume_data
            
            try:
                volume_float = float(h24_volume)
                # 只保存DEX交易量在指定范围内的代币
                if volume_float < min_volume or volume_float > max_volume:
                    continue
            except (ValueError, TypeError):
                continue
            
            # 获取DEX交易池数量
            dex_pools_count = 0
            if "relationships" in dex_token_data and "top_pools" in dex_token_data["relationships"]:
                pools_data = dex_token_data["relationships"]["top_pools"].get("data", [])
                dex_pools_count = len(pools_data)
            
            dex_info = {
                "dex_volume_usd_24h": h24_volume,
                "price_usd": attributes.get("price_usd", "0"),
                "market_cap_usd": attributes.get("market_cap_usd", "0"),
                "total_supply": attributes.get("total_supply", "0"), 
                "dex_pools": dex_pools_count,
                "updated_at": attributes.get("updated_at")
            }
            
            merged_token = {
                "symbol": symbol,
                "name": gate_token.get("name"),
                "contract_address": contract_address,
                "gate_volume_usdt": gate_token.get("volume", 0),
                "dex_data": dex_info
            }
            
            merged_tokens.append(merged_token)
            logger.info(f"保存符合条件的代币: {symbol}, 网络: {network}, DEX交易量: {h24_volume}")
        
        # 添加到结果
        if merged_tokens:
            result["tokens_by_network"][network] = merged_tokens
    
    return result


def save_results(data: Dict, output_dir: str = OUTPUT_DIR) -> str:
    """
    保存结果到JSON文件
    
    Args:
        data: 要保存的数据
        output_dir: 输出目录
        
    Returns:
        保存的文件路径
    """
    os.makedirs(output_dir, exist_ok=True)
    
    # 使用固定文件名
    file_path = os.path.join(output_dir, "gate_dex_volume_comparison.json")
    
    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        logger.info(f"结果已保存到: {file_path}")
        return file_path
    except Exception as e:
        logger.error(f"保存结果失败: {str(e)}")
        return ""


async def main():
    """主函数"""
    logger.info(f"设置交易量范围: {MIN_VOLUME}-{MAX_VOLUME}")
    logger.info("开始处理Gate.io低交易量代币DEX数据...")
    
    # 1. 加载Gate.io代币数据
    gate_tokens = load_gate_tokens()
    if not gate_tokens:
        logger.error("无法加载Gate.io代币数据，退出程序")
        return
    
    # 排除BLASTETH网络
    if "BLASTETH" in gate_tokens:
        logger.info("排除BLASTETH网络数据")
        del gate_tokens["BLASTETH"]
    
    # 2. 处理代币数据并获取DEX交易量
    dex_data = await process_tokens_by_network(gate_tokens)
    
    # 3. 合并数据
    merged_data = merge_data(gate_tokens, dex_data, MIN_VOLUME, MAX_VOLUME)
    
    # 4. 保存结果
    save_results(merged_data)
    
    # 5. 生成统计信息
    total_tokens = sum(len(tokens) for tokens in merged_data["tokens_by_network"].values())
    tokens_with_dex_data = sum(
        sum(1 for token in tokens if token.get("dex_data") and 
            token["dex_data"].get("dex_volume_usd_24h") and 
            MIN_VOLUME <= float(token["dex_data"]["dex_volume_usd_24h"]) <= MAX_VOLUME) 
        for tokens in merged_data["tokens_by_network"].values()
    )
    
    # 生成交易量区间统计
    volume_ranges = {
        f"0-{MIN_VOLUME}": 0,
        f"{MIN_VOLUME}-{MAX_VOLUME}": 0,
        f">{MAX_VOLUME}": 0
    }
    
    for network, tokens in merged_data["tokens_by_network"].items():
        for token in tokens:
            if token.get("dex_data") and token["dex_data"].get("dex_volume_usd_24h"):
                try:
                    vol = float(token["dex_data"]["dex_volume_usd_24h"])
                    if vol == 0:
                        continue
                    elif vol < MIN_VOLUME:
                        volume_ranges[f"0-{MIN_VOLUME}"] += 1
                    elif vol <= MAX_VOLUME:
                        volume_ranges[f"{MIN_VOLUME}-{MAX_VOLUME}"] += 1
                    else:
                        volume_ranges[f">{MAX_VOLUME}"] += 1
                except (ValueError, TypeError):
                    pass
    
    logger.info(f"处理完成! 总计处理了 {total_tokens} 个代币，其中 {tokens_with_dex_data} 个在DEX上有交易量数据 ({MIN_VOLUME}-{MAX_VOLUME})")
    logger.info(f"交易量分布: {volume_ranges}")
    
    # 添加更详细的统计信息
    for network, tokens in merged_data["tokens_by_network"].items():
        tokens_with_volume = sum(1 for token in tokens if token.get("dex_data") and 
                                token["dex_data"].get("dex_volume_usd_24h") and 
                                MIN_VOLUME <= float(token["dex_data"]["dex_volume_usd_24h"]) <= MAX_VOLUME)
        logger.info(f"网络 {network}: 共 {len(tokens)} 个代币，其中 {tokens_with_volume} 个有DEX交易量 ({MIN_VOLUME}-{MAX_VOLUME})")


if __name__ == "__main__":
    # 解决Windows上的事件循环问题
    if platform.system() == 'Windows':
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
    
    # 创建输出目录
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    
    # 运行主函数
    asyncio.run(main()) 