{"version": 3, "file": "uuid.js", "sourceRoot": "", "sources": ["../../src/uuid.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;EAeE;;;AAEF;;GAEG;AAEH,mDAA6C;AAC7C,2CAA0C;AAE1C;;;;;;;;;GASG;AACI,MAAM,MAAM,GAAG,GAAW,EAAE;IAClC,MAAM,KAAK,GAAG,IAAA,uBAAW,EAAC,EAAE,CAAC,CAAC;IAE9B,iIAAiI;IACjI,kBAAkB;IAClB,wCAAwC;IACxC,8BAA8B;IAC9B,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC;IAEpC,cAAc;IACd,uCAAuC;IACvC,uCAAuC;IACvC,8BAA8B;IAC9B,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC;IAEpC,MAAM,SAAS,GAAG,IAAA,0BAAU,EAAC,KAAK,CAAC,CAAC;IAEpC,OAAO;QACN,SAAS,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;QAC1B,SAAS,CAAC,SAAS,CAAC,EAAE,EAAE,EAAE,CAAC;QAC3B,SAAS,CAAC,SAAS,CAAC,EAAE,EAAE,EAAE,CAAC;QAC3B,SAAS,CAAC,SAAS,CAAC,EAAE,EAAE,EAAE,CAAC;QAC3B,SAAS,CAAC,SAAS,CAAC,EAAE,EAAE,EAAE,CAAC;KAC3B,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACb,CAAC,CAAC;AAxBW,QAAA,MAAM,UAwBjB"}