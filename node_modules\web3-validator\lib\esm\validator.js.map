{"version": 3, "file": "validator.js", "sourceRoot": "", "sources": ["../../src/validator.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;EAeE;AACF,OAAO,EAAE,iBAAiB,EAAE,MAAM,aAAa,CAAC;AAGhD,OAAO,EAAE,CAAC,EAAqB,YAAY,EAAc,MAAM,KAAK,CAAC;AAGrE,OAAO,EAAE,kBAAkB,EAAE,MAAM,aAAa,CAAC;AAEjD,OAAO,OAAO,MAAM,cAAc,CAAC;AAEnC,MAAM,YAAY,GAAG,CAAC,MAAkB,EAAW,EAAE;IACpD,IAAI,CAAC,CAAC,CAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,IAAI,CAAA,IAAI,CAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,IAAI,MAAK,QAAQ,CAAC,KAAI,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,UAAU,CAAA,EAAE;QACvE,MAAM,GAAG,GAA+B,EAAE,CAAC;QAC3C,KAAK,MAAM,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,EAAE;YAClD,MAAM,KAAK,GAAG,YAAY,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;YACpD,IAAI,KAAK,EAAE;gBACV,GAAG,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;aAClB;SACD;QAED,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE;YACnC,OAAO,CAAC;iBACN,MAAM,CAAC,GAAG,CAAC;iBACX,OAAO,EAAE;iBACT,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAS,EAAE,EAAE,CAAC,iCAAM,GAAG,KAAE,CAAC,CAAC,CAAC,EAAE,IAAI,IAAG,EAAE,EAAE,CAAC,CAAC,CAAC;SACpF;QACD,OAAO,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,CAAC;KAC/B;IAED,IAAI,CAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,IAAI,MAAK,OAAO,KAAI,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,KAAK,CAAA,EAAE;QAC9C,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,MAAM,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC;eACnD,MAAM,CAAC,QAAQ,KAAK,SAAS;eAC7B,IAAI,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAgB,EAAE,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,KAAK,MAAM,CAAC,KAAK,CAAC,MAAM,EAAE;YAC9F,MAAM,GAAG,GAA2C,EAAE,CAAC;YACvD,KAAK,MAAM,IAAI,IAAI,MAAM,CAAC,KAAK,EAAE;gBAChC,MAAM,KAAK,GAAG,YAAY,CAAC,IAAI,CAAC,CAAC;gBACjC,IAAI,KAAK,EAAE;oBACV,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;iBAChB;aACD;YACD,OAAO,CAAC,CAAC,KAAK,CAAC,GAAoC,CAAC,CAAC;SACrD;QACD,MAAM,UAAU,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;QAC1E,IAAI,cAAc,GAAG,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC,CAAC;QAEvD,cAAc,GAAG,MAAM,CAAC,QAAQ,KAAK,SAAS,CAAC,CAAC,CAAC,cAAc,CAAC,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC;QACtG,cAAc,GAAG,MAAM,CAAC,QAAQ,KAAK,SAAS,CAAC,CAAC,CAAC,cAAc,CAAC,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC;QAC5G,OAAO,cAAc,CAAC;KACtB;IAED,IAAI,MAAM,CAAC,KAAK,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;QAChD,OAAO,CAAC,CAAC,KAAK,CACb,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE,CAAC,YAAY,CAAC,WAAW,CAAC,CAIxD,CACD,CAAC;KACF;IAED,IAAI,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,MAAM,EAAE;QACnB,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;YAC5B,MAAM,IAAI,iBAAiB,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;SAC3C;QAED,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,KAAc,EAAE,EAAE,CAAC,CAAC;YAClE,MAAM,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE;SACxC,CAAC,CAAC,CAAC;KACJ;IAED,IACC,CAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,IAAI;QACZ,CAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,IAAI,MAAK,QAAQ;QACzB,OAAQ,CAAyE,CAChF,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CACnB,KAAK,UAAU,EACf;QACD,OAAQ,CAAyE,CAChF,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CACnB,EAAE,CAAC;KACJ;IACD,OAAO,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC;AAC9C,CAAC,CAAC;AAEF,MAAM,OAAO,SAAS;IAIrB,wFAAwF;IACjF,MAAM,CAAC,OAAO;QACpB,IAAI,CAAC,SAAS,CAAC,iBAAiB,EAAE;YACjC,SAAS,CAAC,iBAAiB,GAAG,IAAI,SAAS,EAAE,CAAC;SAC9C;QACD,OAAO,SAAS,CAAC,iBAAiB,CAAC;IACpC,CAAC;IAEM,QAAQ,CAAC,MAAkB,EAAE,IAAU,EAAE,OAA8B;;QAC7E,MAAM,GAAG,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC;QACjC,MAAM,MAAM,GAAG,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QACnC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE;YACpB,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,MAAA,MAAA,MAAM,CAAC,KAAK,0CAAE,MAAM,mCAAI,EAAE,CAAC,CAAC;YAC9D,IAAI,MAAM,EAAE;gBACX,IAAI,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,MAAM,EAAE;oBACpB,OAAO,MAAM,CAAC;iBACd;gBACD,MAAM,IAAI,kBAAkB,CAAC,MAAM,CAAC,CAAC;aACrC;SACD;QACD,OAAO,SAAS,CAAC;IAClB,CAAC;IACD,kDAAkD;IAC1C,aAAa,CAAC,MAA8B;QACnD,IAAI,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;YACzD,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC,KAAe,EAAE,EAAE;;gBACrC,IAAI,OAAO,CAAC;gBACZ,IAAI,OAAO,CAAC;gBACZ,IAAI,MAAM,CAAC;gBACX,IAAI,UAAU,CAAC;gBAEf,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBAElC,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;gBACxD,MAAM,YAAY,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBAC1C,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY,CAAC,OAAO,EAAE;oBACxC,OAAO,GAAG,UAAU,CAAC;oBACrB,UAAU,GAAG,GAAG,YAAY,WAAW,CAAC;oBACxC,MAAM,GAAG,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC;oBAClC,OAAO,GAAG,2BAA2B,KAAK,CAAC,OAAO,QAAQ,CAAC;iBAC3D;qBAAM,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY,CAAC,SAAS,EAAE;oBACjD,OAAO,GAAG,UAAU,CAAC;oBACrB,UAAU,GAAG,GAAG,YAAY,WAAW,CAAC;oBACxC,MAAM,GAAG,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC;oBAClC,OAAO,GAAG,4BAA4B,KAAK,CAAC,OAAO,QAAQ,CAAC;iBAC5D;qBAAM,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY,CAAC,MAAM,EAAE;oBAC9C,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,CAAC,MAAA,KAAK,CAAC,MAAM,mCAAI,EAAE,CAG5C,CAAC;oBAEF,IAAI,OAAO,KAAK,KAAK,WAAW,EAAE;wBACjC,OAAO,GAAG,cAAc,UAAU,eAAe,CAAC;qBAClD;yBAAM;wBACN,OAAO,GAAG,UAAU;wBACnB,4EAA4E;wBAC5E,OAAO,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KACrD,UAAU,UAAU,gBAAgB,MAAM,cAAc,CAAC;qBACzD;oBAED,MAAM,GAAG,EAAE,KAAK,EAAE,CAAC;iBACnB;gBAED,OAAO;oBACN,OAAO,EAAE,OAAO,aAAP,OAAO,cAAP,OAAO,GAAI,KAAK;oBACzB,YAAY,EAAE,YAAY,CAAC,CAAC,CAAC,IAAI,YAAY,EAAE,CAAC,CAAC,CAAC,EAAE;oBACpD,UAAU,EAAE,UAAU,CAAC,CAAC,CAAC,IAAI,UAAU,EAAE,CAAC,CAAC,CAAC,GAAG;oBAC/C,mEAAmE;oBACnE,MAAM,EAAE,MAAM,aAAN,MAAM,cAAN,MAAM,GAAI,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE;oBAC1C,OAAO,EAAE,OAAO,aAAP,OAAO,cAAP,OAAO,GAAI,KAAK,CAAC,OAAO;iBACJ,CAAC;YAChC,CAAC,CAAC,CAAC;SACH;QACD,OAAO,SAAS,CAAC;IAClB,CAAC;CACD"}