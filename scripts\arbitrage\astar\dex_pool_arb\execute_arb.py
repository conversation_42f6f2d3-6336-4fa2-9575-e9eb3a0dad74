#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
DEX池子间套利执行脚本
执行两个WASTR/USDT池子间的套利交易
"""

import os
import sys
import asyncio
import argparse
from decimal import Decimal
from typing import Dict, Optional

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../..'))
sys.path.insert(0, project_root)

from src.dex.astar.arthswap.client import ArthSwapClient
from src.utils.logger import logger
from src.utils.config import Config
from src.utils.web3_utils import get_web3

# 池子配置
POOLS = {
    "pool1": {
        "address": "0x806f746a7c4293092ac7aa604347BE123322dF1e",
        "name": "WASTR/USDT Pool"
    },
    "pool2": {
        "address": "0xDdeA1b3343c438c2E2d636D070cfb4F63d26636e",
        "name": "USDT/WASTR Pool"
    }
}

# 交易参数
GAS_PRICE_MULTIPLIER = 1.5  # gas价格倍数
SLIPPAGE = 0.01  # 滑点容忍度(1%)

async def execute_arbitrage(
    amount_usdt: float,
    buy_pool_address: str,
    sell_pool_address: str,
    client: ArthSwapClient
) -> Dict:
    """
    执行套利交易
    
    Args:
        amount_usdt: USDT投入金额
        buy_pool_address: 买入池子地址
        sell_pool_address: 卖出池子地址
        client: ArthSwap客户端
        
    Returns:
        交易结果详情
    """
    try:
        # 第一步交易: USDT -> WASTR
        logger.info(f"开始第一步交易: {amount_usdt} USDT -> WASTR")
        amount_in = Decimal(str(amount_usdt))
        
        tx_hash1 = await client.swap(
            pool_address=buy_pool_address,
            amount_in=amount_in,
            token_in="USDT",
            token_out="WASTR",
            slippage=SLIPPAGE,
            gas_price_multiplier=GAS_PRICE_MULTIPLIER
        )
        
        if not tx_hash1:
            logger.error("第一步交易失败")
            return None
            
        logger.info(f"第一步交易成功: {tx_hash1}")
        
        # 获取WASTR余额
        wastr_balance = await client.get_token_balance("WASTR")
        if not wastr_balance:
            logger.error("无法获取WASTR余额")
            return None
            
        # 第二步交易: WASTR -> USDT
        logger.info(f"开始第二步交易: {float(wastr_balance):.4f} WASTR -> USDT")
        
        tx_hash2 = await client.swap(
            pool_address=sell_pool_address,
            amount_in=wastr_balance,
            token_in="WASTR",
            token_out="USDT",
            slippage=SLIPPAGE,
            gas_price_multiplier=GAS_PRICE_MULTIPLIER
        )
        
        if not tx_hash2:
            logger.error("第二步交易失败")
            return None
            
        logger.info(f"第二步交易成功: {tx_hash2}")
        
        # 获取最终USDT余额
        final_usdt = await client.get_token_balance("USDT")
        if not final_usdt:
            logger.error("无法获取最终USDT余额")
            return None
            
        # 计算利润
        profit = float(final_usdt) - amount_usdt
        
        return {
            "amount_in": amount_usdt,
            "amount_out": float(final_usdt),
            "profit": profit,
            "tx_hash1": tx_hash1,
            "tx_hash2": tx_hash2
        }
        
    except Exception as e:
        logger.error(f"执行套利时发生错误: {str(e)}")
        return None

async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="执行DEX池子间套利")
    parser.add_argument("amount", type=float, help="USDT投入金额")
    parser.add_argument("buy_pool", choices=["pool1", "pool2"], help="买入池子")
    args = parser.parse_args()
    
    # 初始化Web3和客户端
    config = Config()
    web3 = get_web3()
    client = ArthSwapClient(web3)
    
    # 确定卖出池子
    sell_pool = "pool2" if args.buy_pool == "pool1" else "pool1"
    
    # 执行套利
    result = await execute_arbitrage(
        amount_usdt=args.amount,
        buy_pool_address=POOLS[args.buy_pool]["address"],
        sell_pool_address=POOLS[sell_pool]["address"],
        client=client
    )
    
    if result:
        print("\n=== 套利执行结果 ===")
        print(f"投入金额: {result['amount_in']:.4f} USDT")
        print(f"获得金额: {result['amount_out']:.4f} USDT")
        print(f"净利润: {result['profit']:.4f} USDT")
        print(f"\n交易哈希:")
        print(f"USDT -> WASTR: {result['tx_hash1']}")
        print(f"WASTR -> USDT: {result['tx_hash2']}")
        print("\n✨ 套利执行完成!")
    else:
        print("\n❌ 套利执行失败")

if __name__ == "__main__":
    asyncio.run(main()) 