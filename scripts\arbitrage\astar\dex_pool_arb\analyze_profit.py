#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
DEX池子间套利分析脚本
使用二分法分析两个WASTR/USDT池子间的最佳套利机会
"""

import os
import sys
from decimal import Decimal
from typing import Dict, Tuple, Optional

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../..'))
sys.path.insert(0, project_root)

from scripts.dex.local_swap_simulator import LocalSwapSimulator
from src.utils.logger import logger

# 池子配置
POOLS = {
    "pool1": {
        "address": "0x806f746a7c4293092ac7aa604347BE123322dF1e",
        "name": "ArthSwap Pool1 WASTR/USDT",
        "token0": "USDT",
        "token1": "WASTR"
    },
    "pool2": {
        "address": "0xDdeA1b3343c438c2E2d636D070cfb4F63d26636e",
        "name": "ArthSwap Pool2 WASTR/USDT",
        "token0": "WASTR",
        "token1": "USDT"
    }
}

# 套利参数
MIN_USDT = Decimal('1')      # 最小USDT投入
MAX_USDT = Decimal('250')    # 最大USDT投入
BINARY_SEARCH_TOLERANCE = Decimal('0.1')  # 二分查找精度
GAS_COST_ASTR = Decimal('2.5') # 预估gas成本(一次swap中完成套利)
MIN_PROFIT = Decimal('0.001')    # 最小获利(USDT)

def get_wastr_usdt_price(simulator: LocalSwapSimulator, is_pool1: bool) -> Optional[Decimal]:
    """
    获取统一格式的 WASTR/USDT 价格
    
    Args:
        simulator: 池子模拟器
        is_pool1: 是否是Pool1
        
    Returns:
        WASTR/USDT 价格 (1 WASTR = ? USDT)
    """
    try:
        if is_pool1:
            # Pool1: USDT-WASTR，使用 1/price_1_per_0
            # price_1_per_0 返回 WASTR/USDT，需要取倒数
            price = simulator.get_price_1_per_0()
            if not price:
                return None
            return Decimal('1') / price
        else:
            # Pool2: WASTR-USDT，使用 1/price_0_per_1
            # price_0_per_1 返回 WASTR/USDT，需要取倒数
            price = simulator.get_price_0_per_1()
            if not price:
                return None
            return Decimal('1') / price
            
    except Exception as e:
        logger.error(f"计算WASTR/USDT价格出错: {e}")
        return None

def simulate_swap_usdt_to_wastr(simulator: LocalSwapSimulator, amount_usdt: Decimal, is_pool1: bool) -> Optional[Decimal]:
    """
    模拟 USDT 换 WASTR
    
    Args:
        simulator: 池子模拟器
        amount_usdt: USDT数量
        is_pool1: 是否是Pool1
        
    Returns:
        获得的WASTR数量
    """
    try:
        if is_pool1:
            # Pool1: USDT(token0) -> WASTR(token1)
            return simulator.simulate_swap_0_to_1(amount_usdt)
        else:
            # Pool2: USDT(token1) -> WASTR(token0)
            return simulator.simulate_swap_1_to_0(amount_usdt)
    except Exception as e:
        logger.error(f"模拟USDT换WASTR失败: {e}")
        return None

def simulate_swap_wastr_to_usdt(simulator: LocalSwapSimulator, amount_wastr: Decimal, is_pool1: bool) -> Optional[Decimal]:
    """
    模拟 WASTR 换 USDT
    
    Args:
        simulator: 池子模拟器
        amount_wastr: WASTR数量
        is_pool1: 是否是Pool1
        
    Returns:
        获得的USDT数量
    """
    try:
        if is_pool1:
            # Pool1: WASTR(token1) -> USDT(token0)
            return simulator.simulate_swap_1_to_0(amount_wastr)
        else:
            # Pool2: WASTR(token0) -> USDT(token1)
            return simulator.simulate_swap_0_to_1(amount_wastr)
    except Exception as e:
        logger.error(f"模拟WASTR换USDT失败: {e}")
        return None

def analyze_arbitrage(amount_usdt: Decimal, sim1: LocalSwapSimulator, sim2: LocalSwapSimulator) -> Optional[Dict]:
    """
    分析特定金额的套利机会
    
    Args:
        amount_usdt: USDT投入金额
        sim1: 第一个池子模拟器
        sim2: 第二个池子模拟器
        
    Returns:
        套利机会详情，如果无机会则返回None
    """
    try:
        # 获取当前价格
        price1 = get_wastr_usdt_price(sim1, True)
        price2 = get_wastr_usdt_price(sim2, False)
        
        if not price1 or not price2:
            logger.warning("无法获取价格")
            return None
            
        logger.info(f"Pool1 WASTR/USDT价格: {float(price1):.6f}")
        logger.info(f"Pool2 WASTR/USDT价格: {float(price2):.6f}")
        
        # 确定买入和卖出池子
        if price1 < price2:  # 在价格低的池子买入
            buy_pool = sim1
            sell_pool = sim2
            buy_price = price1
            sell_price = price2
            is_buy_pool1 = True
            logger.info(f"价差分析: Pool1价格低于Pool2，考虑在Pool1买入WASTR，在Pool2卖出")
        else:
            buy_pool = sim2
            sell_pool = sim1
            buy_price = price2
            sell_price = price1
            is_buy_pool1 = False
            logger.info(f"价差分析: Pool2价格低于Pool1，考虑在Pool2买入WASTR，在Pool1卖出")
        
        price_diff_percent = (sell_price - buy_price) / buy_price * 100
        logger.info(f"价差百分比: {float(price_diff_percent):.4f}%")
        
        # 模拟交易
        logger.info(f"\n尝试套利金额: {float(amount_usdt):.2f} USDT")
        
        # 买入WASTR
        wastr_amount = simulate_swap_usdt_to_wastr(buy_pool, amount_usdt, is_buy_pool1)
        if not wastr_amount:
            logger.warning("买入WASTR模拟失败")
            return None
        logger.info(f"预计买入: {float(wastr_amount):.6f} WASTR")
        
        # 卖出WASTR
        usdt_amount = simulate_swap_wastr_to_usdt(sell_pool, wastr_amount, not is_buy_pool1)
        if not usdt_amount:
            logger.warning("卖出WASTR模拟失败")
            return None
        logger.info(f"预计卖出获得: {float(usdt_amount):.6f} USDT")
        
        # 计算利润
        gross_profit = usdt_amount - amount_usdt
        gas_cost_usdt = GAS_COST_ASTR * buy_price  # 使用买入价格计算gas成本
        net_profit = gross_profit - gas_cost_usdt
        
        logger.info(f"\n利润分析:")
        logger.info(f"毛利润: {float(gross_profit):.6f} USDT")
        logger.info(f"Gas成本: {float(gas_cost_usdt):.6f} USDT")
        logger.info(f"净利润: {float(net_profit):.6f} USDT")
        
        if net_profit <= MIN_PROFIT:
            logger.info(f"净利润 {float(net_profit):.6f} USDT 低于最小盈利要求 {float(MIN_PROFIT):.6f} USDT")
            return None
        
        return {
            "amount_usdt": float(amount_usdt),
            "wastr_amount": float(wastr_amount),
            "received_usdt": float(usdt_amount),
            "buy_pool": buy_pool.pool_name,
            "sell_pool": sell_pool.pool_name,
            "buy_price": float(buy_price),
            "sell_price": float(sell_price),
            "price_diff": float(price_diff_percent),
            "gross_profit": float(gross_profit),
            "gas_cost": float(gas_cost_usdt),
            "net_profit": float(net_profit)
        }
        
    except Exception as e:
        logger.error(f"分析套利机会时出错: {e}")
        return None

def binary_search_best_amount(sim1: LocalSwapSimulator, sim2: LocalSwapSimulator) -> Optional[Dict]:
    """
    使用二分法寻找最佳套利金额
    
    Args:
        sim1: 第一个池子模拟器
        sim2: 第二个池子模拟器
        
    Returns:
        最佳套利机会详情，如果无机会则返回None
    """
    left = MIN_USDT
    right = MAX_USDT
    best_opportunity = None
    
    while right - left > BINARY_SEARCH_TOLERANCE:
        mid1 = left + (right - left) / Decimal('3')
        mid2 = right - (right - left) / Decimal('3')
        
        # 分析两个中点的套利机会
        opp1 = analyze_arbitrage(mid1, sim1, sim2)
        opp2 = analyze_arbitrage(mid2, sim1, sim2)
        
        # 如果两个机会都无效，缩小范围继续查找
        if not opp1 and not opp2:
            right = mid2
            continue
            
        # 比较两个机会的利润
        profit1 = Decimal(str(opp1["net_profit"])) if opp1 else Decimal('0')
        profit2 = Decimal(str(opp2["net_profit"])) if opp2 else Decimal('0')
        
        # 更新最佳机会
        if profit1 > Decimal('0') or profit2 > Decimal('0'):
            if profit1 > profit2:
                best_opportunity = opp1
                right = mid2
            else:
                best_opportunity = opp2
                left = mid1
        else:
            right = mid2
    
    return best_opportunity

def find_best_opportunity() -> Tuple[Dict, Optional[Dict]]:
    """
    寻找最佳套利机会
    
    Returns:
        (池子状态, 最佳机会)
    """
    # 创建池子模拟器
    sim1 = LocalSwapSimulator(
        token0=POOLS["pool1"]["token0"],
        token1=POOLS["pool1"]["token1"],
        pool_address=POOLS["pool1"]["address"],
        pool_name=POOLS["pool1"]["name"]
    )
    
    sim2 = LocalSwapSimulator(
        token0=POOLS["pool2"]["token0"],
        token1=POOLS["pool2"]["token1"],
        pool_address=POOLS["pool2"]["address"],
        pool_name=POOLS["pool2"]["name"]
    )
    
    # 获取池子状态
    if not (sim1.update_reserves() and sim2.update_reserves()):
        return None, None
    
    # 记录池子状态
    pool_status = {
        "pool1": {
            "name": sim1.pool_name,
            "reserves": [float(x) for x in sim1.get_reserves()],
            "price": float(get_wastr_usdt_price(sim1, True) or 0)
        },
        "pool2": {
            "name": sim2.pool_name,
            "reserves": [float(x) for x in sim2.get_reserves()],
            "price": float(get_wastr_usdt_price(sim2, False) or 0)
        }
    }
    
    # 使用二分法寻找最佳机会
    best_opportunity = binary_search_best_amount(sim1, sim2)
    
    return pool_status, best_opportunity

def main():
    """主函数"""
    # 分析套利机会
    pool_status, opportunity = find_best_opportunity()
    
    if not pool_status:
        logger.error("无法获取池子状态")
        return
    
    # 显示池子状态
    print("\n=== 池子状态 ===")
    for pool_id, status in pool_status.items():
        print(f"\n{status['name']}:")
        if pool_id == "pool1":
            print(f"USDT储备: {status['reserves'][0]:.2f}")
            print(f"WASTR储备: {status['reserves'][1]:.2f}")
        else:
            print(f"WASTR储备: {status['reserves'][0]:.2f}")
            print(f"USDT储备: {status['reserves'][1]:.2f}")
        print(f"当前价格: {status['price']:.6f} WASTR/USDT")
    
    # 显示套利机会
    if opportunity and opportunity["net_profit"] > float(MIN_PROFIT):
        print("\n=== 最佳套利机会 ===")
        print(f"投入金额: {opportunity['amount_usdt']:.2f} USDT")
        print(f"买入池子: {opportunity['buy_pool']}")
        print(f"卖出池子: {opportunity['sell_pool']}")
        print(f"买入价格: {opportunity['buy_price']:.6f} WASTR/USDT")
        print(f"卖出价格: {opportunity['sell_price']:.6f} WASTR/USDT")
        print(f"价差: {opportunity['price_diff']:.2f}%")
        print(f"\n交易详情:")
        print(f"USDT -> WASTR: {opportunity['wastr_amount']:.6f}")
        print(f"WASTR -> USDT: {opportunity['received_usdt']:.6f}")
        print(f"\n利润分析:")
        print(f"毛利润: {opportunity['gross_profit']:.6f} USDT")
        print(f"Gas成本: {opportunity['gas_cost']:.6f} USDT")
        print(f"净利润: {opportunity['net_profit']:.6f} USDT")
        print("\n✨ 发现有利可图的套利机会!")
    else:
        print("\n当前无套利机会")

if __name__ == "__main__":
    main() 