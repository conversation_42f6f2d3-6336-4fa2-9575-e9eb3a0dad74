2025-06-13 03:39:25,890 - trade_MASK - INFO - MASK 交易线程已启动
2025-06-13 03:39:25,890 - trade_MASK - INFO - 开始执行 MASK 买入操作
2025-06-13 03:39:25,890 - trade_MASK - INFO - 链: ethereum
2025-06-13 03:39:25,890 - trade_MASK - INFO - 代币地址: ******************************************
2025-06-13 03:39:25,890 - trade_MASK - INFO - USDT数量: 220.94
2025-06-13 03:39:25,890 - trade_MASK - INFO - 使用USDT精度: 6
2025-06-13 03:39:25,891 - trade_MASK - INFO - 获取到代币精度: 18
2025-06-13 03:39:25,891 - trade_MASK - INFO - 获取交易路由...
2025-06-13 03:39:28,695 - trade_MASK - INFO - 买入交易详情:
2025-06-13 03:39:28,695 - trade_MASK - INFO -   预期输出数量: 145.67515560505564 MASK
2025-06-13 03:39:28,695 - trade_MASK - INFO -   预估Gas成本: 1.06578105891289 USD
2025-06-13 03:39:28,695 - trade_MASK - INFO -   交易路由: [[{'pool': 'kyber_pmm_******************************************_0xa0b86991c6218b36c1d19d4a2e9eb0ce3606eb48_******************************************_0xc02aaa39b223fe8d0a0e5c4f27ead9083c756cc2', 'tokenIn': '******************************************', 'tokenOut': '******************************************', 'swapAmount': '220940000', 'amountOut': '145675155605055627982', 'exchange': 'kyber-pmm', 'poolType': 'kyber-pmm', 'poolExtra': {'timestamp': 1749757167}, 'extra': {'makerAsset': '******************************************', 'makingAmount': '146092627342254866432', 'ri': 'de3f36ff-a7a3-4cba-b70a-06984fef87bf', 'takerAsset': '******************************************', 'takingAmount': '220940000'}}]]
2025-06-13 03:39:28,695 - trade_MASK - INFO - 开始执行实际交易...
2025-06-13 03:39:28,695 - trade_MASK - ERROR - 执行买入操作时出错: swap_tokens() got an unexpected keyword argument 'amount_in'
2025-06-13 03:39:28,696 - trade_MASK - ERROR - Traceback (most recent call last):
  File "C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\scripts\arbitrage\portal_polygon_bridge\bridge_arb_executor.py", line 199, in execute_buy
    swap_result = await swap_tokens(
                        ^^^^^^^^^^^^
TypeError: swap_tokens() got an unexpected keyword argument 'amount_in'

