#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
获取Gate.io上指定代币的USDT交易对订单簿信息
读取gate_dex_volume_comparison.json中的代币列表，并获取每个代币的订单簿信息
"""

import os
import sys
import json
import time
import argparse
import logging
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed

# 添加项目根目录到路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../..'))
sys.path.insert(0, project_root)

# 导入Gate.io客户端
try:
    from src.cex.gate.client import GateClient
except ImportError:
    print("无法导入GateClient，请检查路径和环境设置")
    sys.exit(1)

# 创建自定义logger
def setup_custom_logger(name, log_file=None):
    """
    设置自定义日志工具
    
    Args:
        name: 日志器名称
        log_file: 日志文件路径
    
    Returns:
        配置好的日志器
    """
    logger = logging.getLogger(name)
    logger.setLevel(logging.INFO)
    
    # 清除现有处理器
    if logger.handlers:
        logger.handlers.clear()
    
    # 创建格式器
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    
    # 创建控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # 创建文件处理器
    if log_file:
        os.makedirs(os.path.dirname(log_file), exist_ok=True)
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    
    return logger

# 设置日志
log_file = os.path.join(project_root, 'logs', 'gate_order_book_collector.log')
logger = setup_custom_logger('gate_order_book_collector', log_file)

# 定义默认路径
DEX_VOLUME_FILE = os.path.join(project_root, 'data', 'cex', 'gate_info', 'dex_vol', 'gate_dex_volume_comparison.json')

def load_dex_volume_data(file_path):
    """
    加载DEX交易量比较数据
    
    Args:
        file_path: gate_dex_volume_comparison.json文件路径
        
    Returns:
        dict: 包含所有代币信息的字典
    """
    try:
        logger.info(f"正在从 {file_path} 加载代币数据...")
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 提取所有网络中的代币
        all_tokens = []
        tokens_by_network = data.get('tokens_by_network', {})
        
        for network, tokens in tokens_by_network.items():
            logger.info(f"从 {network} 网络加载了 {len(tokens)} 个代币")
            for token in tokens:
                # 添加网络信息到代币数据中
                token['network'] = network
                all_tokens.append(token)
        
        logger.info(f"总共加载了 {len(all_tokens)} 个代币")
        return all_tokens
    except Exception as e:
        logger.error(f"加载DEX交易量数据时出错: {e}")
        return []

def get_order_book(client, symbol):
    """
    获取指定代币的订单簿信息
    
    Args:
        client: GateClient实例
        symbol: 交易对，例如BTC/USDT
        
    Returns:
        tuple: (是否成功获取订单簿, 订单簿数据)
    """
    try:
        # 直接使用GateClient获取订单簿
        order_book = client.get_order_book(symbol, limit=10)
        
        # 记录订单簿信息到日志
        if order_book and 'bids' in order_book and 'asks' in order_book:
            bids = order_book.get('bids', [])
            asks = order_book.get('asks', [])
            
            logger.info(f"成功获取 {symbol} 的订单簿信息:")
            
            # 记录最佳买入价和卖出价
            best_bid = bids[0][0] if bids else "N/A"
            best_ask = asks[0][0] if asks else "N/A"
            
            logger.info(f"  最佳买入价: {best_bid}")
            logger.info(f"  最佳卖出价: {best_ask}")
            
            # 计算价差
            if bids and asks:
                spread = float(asks[0][0]) - float(bids[0][0])
                spread_percent = (spread / float(bids[0][0])) * 100
                logger.info(f"  价差: {spread:.8f} ({spread_percent:.2f}%)")
            
            # 买单信息
            logger.info(f"  买单(Bids)前{len(bids)}条:")
            for i, (price, amount) in enumerate(bids[:5]):
                logger.info(f"    #{i+1}: 价格={price}, 数量={amount}")
            
            # 卖单信息
            logger.info(f"  卖单(Asks)前{len(asks)}条:")
            for i, (price, amount) in enumerate(asks[:5]):
                logger.info(f"    #{i+1}: 价格={price}, 数量={amount}")
            
            return True, order_book
        else:
            logger.error(f"获取 {symbol} 的订单簿失败: 数据不完整")
            return False, None
    except Exception as e:
        logger.error(f"获取 {symbol} 的订单簿时出错: {str(e)}")
        return False, None

def collect_order_books(tokens, max_workers, min_volume=0):
    """
    收集所有代币的订单簿信息（一次性）
    
    Args:
        tokens: 代币列表
        max_workers: 最大并发线程数
        min_volume: 最小交易量阈值，只处理交易量大于此值的代币
    """
    # 过滤交易量过小的代币
    if min_volume > 0:
        filtered_tokens = [t for t in tokens if t.get('gate_volume_usdt', 0) >= min_volume]
        logger.info(f"根据最小交易量 {min_volume} USDT，从 {len(tokens)} 个代币中过滤得到 {len(filtered_tokens)} 个代币")
        tokens = filtered_tokens
    
    # 创建Gate.io客户端
    try:
        client = GateClient()
        logger.info("成功创建Gate.io客户端")
    except Exception as e:
        logger.error(f"创建Gate.io客户端时出错: {str(e)}")
        return 0, 0
    
    start_time = time.time()
    logger.info(f"开始收集订单簿数据，处理 {len(tokens)} 个代币...")
    
    # 使用线程池并行获取订单簿
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        futures = {}
        for token in tokens:
            symbol = token.get('symbol')
            if not symbol:
                continue
            
            # 构建交易对
            trading_pair = f"{symbol}/USDT"
            
            # 提交任务到线程池
            futures[executor.submit(get_order_book, client, trading_pair)] = trading_pair
        
        # 等待所有任务完成
        successful = 0
        failed = 0
        for future in as_completed(futures):
            trading_pair = futures[future]
            try:
                is_success, _ = future.result()
                if is_success:
                    successful += 1
                else:
                    failed += 1
            except Exception as e:
                logger.error(f"处理 {trading_pair} 时出错: {str(e)}")
                failed += 1
    
    # 记录统计信息
    logger.info(f"订单簿数据收集完成: 成功 {successful} 个, 失败 {failed} 个")
    
    # 计算执行时间
    elapsed = time.time() - start_time
    logger.info(f"总耗时: {elapsed:.2f} 秒")
    
    return successful, failed

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="获取Gate.io代币订单簿信息")
    parser.add_argument("--threads", type=int, default=5, help="最大并发线程数，默认5")
    parser.add_argument("--input", type=str, default=DEX_VOLUME_FILE, help="DEX交易量比较文件路径")
    parser.add_argument("--min-volume", type=float, default=0, help="最小交易量阈值，只处理交易量大于此值的代币，默认0（不过滤）")
    args = parser.parse_args()
    
    # 显示启动参数
    logger.info(f"启动参数: 线程数={args.threads}, 输入文件={args.input}, 最小交易量={args.min_volume}")
    
    try:
        # 加载代币数据
        tokens = load_dex_volume_data(args.input)
        
        if not tokens:
            logger.error("未加载到任何代币数据，请检查输入文件")
            return
        
        # 开始收集数据（一次性）
        successful, failed = collect_order_books(tokens, args.threads, args.min_volume)
        logger.info(f"订单簿数据收集任务完成，成功: {successful}，失败: {failed}")
        
    except KeyboardInterrupt:
        logger.info("用户中断，程序退出")
    except Exception as e:
        logger.error(f"程序运行时出错: {str(e)}")

if __name__ == "__main__":
    main() 