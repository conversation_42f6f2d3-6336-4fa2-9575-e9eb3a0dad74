{"version": 3, "file": "socket_provider.d.ts", "sourceRoot": "", "sources": ["../../src/socket_provider.ts"], "names": [], "mappings": "AAgBA,OAAO,EACN,eAAe,EAEf,eAAe,EAGf,SAAS,EAGT,eAAe,EACf,yBAAyB,EACzB,aAAa,EACb,mBAAmB,EACnB,eAAe,EACf,gBAAgB,EAChB,iBAAiB,EACjB,aAAa,EACb,cAAc,EACd,iBAAiB,EACjB,WAAW,EACX,gCAAgC,EAChC,yBAAyB,EACzB,gCAAgC,EAChC,kBAAkB,EAClB,MAAM,YAAY,CAAC;AAUpB,OAAO,EAAE,eAAe,EAAE,MAAM,4BAA4B,CAAC;AAC7D,OAAO,EAAE,mBAAmB,EAAE,MAAM,4BAA4B,CAAC;AAKjE,MAAM,MAAM,gBAAgB,GAAG;IAC9B,aAAa,EAAE,OAAO,CAAC;IACvB,KAAK,EAAE,MAAM,CAAC;IACd,WAAW,EAAE,MAAM,CAAC;CACpB,CAAC;AAUF,8BAAsB,cAAc,CACnC,YAAY,EACZ,UAAU,EACV,UAAU,EACV,GAAG,SAAS,WAAW,GAAG,eAAe,CACxC,SAAQ,eAAe,CAAC,GAAG,CAAC;IAC7B,SAAS,CAAC,cAAc,EAAE,OAAO,CAAC;IAClC,SAAS,CAAC,QAAQ,CAAC,WAAW,EAAE,MAAM,CAAC;IACvC,SAAS,CAAC,QAAQ,CAAC,mBAAmB,EAAE,mBAAmB,CAAC;IAE5D,SAAS,CAAC,QAAQ,CAAC,qBAAqB,EAAE,GAAG,CAAC,SAAS,EAAE,iBAAiB,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;IAE3F,SAAS,CAAC,QAAQ,CAAC,kBAAkB,EAAE,GAAG,CAAC,SAAS,EAAE,iBAAiB,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;IACxF,SAAS,CAAC,kBAAkB,EAAG,MAAM,CAAC;IACtC,SAAS,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE,OAAO,CAAC;IAC5C,SAAS,CAAC,QAAQ,CAAC,iBAAiB,EAAE,gBAAgB,CAAC;IACvD,SAAS,CAAC,iBAAiB,CAAC,EAAE,OAAO,CAAC;IACtC,IAAW,gBAAgB,YAE1B;IACD,SAAS,CAAC,iBAAiB,EAAE,kBAAkB,CAAC;IAChD,SAAS,CAAC,QAAQ,CAAC,iBAAiB,EAAE,CAAC,KAAK,EAAE,YAAY,KAAK,IAAI,CAAC;IACpE,SAAS,CAAC,QAAQ,CAAC,cAAc,EAAE,MAAM,IAAI,CAAC;IAC9C,SAAS,CAAC,QAAQ,CAAC,eAAe,EAAE,CAAC,KAAK,EAAE,UAAU,KAAK,IAAI,CAAC;IAChE,SAAS,CAAC,QAAQ,CAAC,eAAe,EAAE,CAAC,KAAK,EAAE,UAAU,KAAK,IAAI,CAAC;IAEhE;;;;;OAKG;gBAEF,UAAU,EAAE,MAAM,EAClB,aAAa,CAAC,EAAE,OAAO,EACvB,gBAAgB,CAAC,EAAE,OAAO,CAAC,gBAAgB,CAAC;IAmC7C,SAAS,CAAC,KAAK;IAIf;;OAEG;IACI,OAAO,IAAI,IAAI;IAyBtB,SAAS,CAAC,QAAQ,CAAC,qBAAqB,IAAI,IAAI;IAChD,SAAS,CAAC,QAAQ,CAAC,mBAAmB,IAAI,IAAI;IAE9C,SAAS,CAAC,QAAQ,CAAC,sBAAsB,IAAI,IAAI;IAEjD,SAAS,CAAC,QAAQ,CAAC,aAAa,CAAC,MAAM,EAAE,OAAO,GAAG,IAAI;IAEvD,SAAS,CAAC,QAAQ,CAAC,aAAa,CAAC,QAAQ,EAAE,cAAc,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,IAAI;IAE1E,SAAS,CAAC,QAAQ,CAAC,eAAe,CAAC,MAAM,EAAE,YAAY,GAAG,eAAe,EAAE;IAE3E,SAAS,CAAC,QAAQ,CAAC,sBAAsB,CAAC,KAAK,CAAC,EAAE,MAAM,EAAE,KAAK,CAAC,EAAE,MAAM,GAAG,IAAI;IAG/E,SAAS,CAAC,qBAAqB,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO;IAItD;;;OAGG;IAEI,0BAA0B;IAIjC;;;OAGG;IAEI,wBAAwB;IAI/B;;;OAGG;IAEI,qBAAqB,IAAI,OAAO;IAIvC;;;;OAIG;IACI,EAAE,CACR,IAAI,EAAE,YAAY,EAClB,QAAQ,EAAE,gCAAgC,CAAC,gBAAgB,CAAC,GAC1D,IAAI;IACA,EAAE,CACR,IAAI,EAAE,SAAS,EACf,QAAQ,EAAE,gCAAgC,CAAC,mBAAmB,CAAC,GAC7D,IAAI;IACA,EAAE,CAAC,IAAI,EAAE,cAAc,EAAE,QAAQ,EAAE,gCAAgC,CAAC,MAAM,CAAC,GAAG,IAAI;IAClF,EAAE,CAAC,IAAI,EAAE,iBAAiB,EAAE,QAAQ,EAAE,gCAAgC,CAAC,MAAM,EAAE,CAAC,GAAG,IAAI;IACvF,EAAE,CAAC,CAAC,GAAG,aAAa,EAC1B,IAAI,EAAE,SAAS,EACf,QAAQ,EACL,gCAAgC,CAAC,eAAe,CAAC,GACjD,gCAAgC,CAAC,CAAC,CAAC,GACpC,IAAI;IACA,EAAE,CAAC,CAAC,GAAG,aAAa,EAC1B,IAAI,EAAE,MAAM,EACZ,QAAQ,EAAE,gCAAgC,CAAC,OAAO,CAAC,GAAG,yBAAyB,CAAC,CAAC,CAAC,GAChF,IAAI;IAWP;;;;OAIG;IACI,IAAI,CACV,IAAI,EAAE,YAAY,EAClB,QAAQ,EAAE,gCAAgC,CAAC,gBAAgB,CAAC,GAC1D,IAAI;IACA,IAAI,CACV,IAAI,EAAE,SAAS,EACf,QAAQ,EAAE,gCAAgC,CAAC,mBAAmB,CAAC,GAC7D,IAAI;IACA,IAAI,CAAC,IAAI,EAAE,cAAc,EAAE,QAAQ,EAAE,gCAAgC,CAAC,MAAM,CAAC,GAAG,IAAI;IACpF,IAAI,CACV,IAAI,EAAE,iBAAiB,EACvB,QAAQ,EAAE,gCAAgC,CAAC,MAAM,EAAE,CAAC,GAClD,IAAI;IACA,IAAI,CAAC,CAAC,GAAG,aAAa,EAC5B,IAAI,EAAE,SAAS,EACf,QAAQ,EACL,gCAAgC,CAAC,eAAe,CAAC,GACjD,gCAAgC,CAAC,CAAC,CAAC,GACpC,IAAI;IACA,IAAI,CAAC,CAAC,GAAG,aAAa,EAC5B,IAAI,EAAE,MAAM,EACZ,QAAQ,EAAE,gCAAgC,CAAC,OAAO,CAAC,GAAG,yBAAyB,CAAC,CAAC,CAAC,GAChF,IAAI;IAWP;;;;OAIG;IACI,cAAc,CACpB,IAAI,EAAE,YAAY,EAClB,QAAQ,EAAE,gCAAgC,CAAC,gBAAgB,CAAC,GAC1D,IAAI;IACA,cAAc,CACpB,IAAI,EAAE,SAAS,EACf,QAAQ,EAAE,gCAAgC,CAAC,mBAAmB,CAAC,GAC7D,IAAI;IACA,cAAc,CACpB,IAAI,EAAE,cAAc,EACpB,QAAQ,EAAE,gCAAgC,CAAC,MAAM,CAAC,GAChD,IAAI;IACA,cAAc,CACpB,IAAI,EAAE,iBAAiB,EACvB,QAAQ,EAAE,gCAAgC,CAAC,MAAM,EAAE,CAAC,GAClD,IAAI;IACA,cAAc,CAAC,CAAC,GAAG,aAAa,EACtC,IAAI,EAAE,SAAS,EACf,QAAQ,EACL,gCAAgC,CAAC,eAAe,CAAC,GACjD,gCAAgC,CAAC,CAAC,CAAC,GACpC,IAAI;IACA,cAAc,CAAC,CAAC,GAAG,aAAa,EACtC,IAAI,EAAE,MAAM,EACZ,QAAQ,EAAE,gCAAgC,CAAC,OAAO,CAAC,GAAG,yBAAyB,CAAC,CAAC,CAAC,GAChF,IAAI;IAWP,SAAS,CAAC,aAAa,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,MAAM;IAKnD;;;;OAIG;IACI,UAAU,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,MAAM,GAAG,IAAI;IASrD;;;;;;OAMG;IACU,cAAc,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,MAAM,EAAE,eAAe,UAAQ,EAAE,EAAE,SAAO;IAuB5F;;;OAGG;IACI,kBAAkB,CAAC,IAAI,EAAE,MAAM,GAAG,IAAI;IAI7C,SAAS,CAAC,QAAQ,CAAC,KAAK,EAAE,UAAU,GAAG,IAAI;IAS3C;;OAEG;IACI,KAAK,IAAI,IAAI;IASpB,SAAS,CAAC,UAAU,IAAI,IAAI;IAiC5B;;OAEG;IACU,OAAO,CACnB,MAAM,SAAS,aAAa,CAAC,GAAG,CAAC,EACjC,UAAU,GAAG,iBAAiB,CAAC,GAAG,EAAE,MAAM,CAAC,EAC1C,OAAO,EAAE,cAAc,CAAC,GAAG,EAAE,MAAM,CAAC,GAAG,OAAO,CAAC,yBAAyB,CAAC,UAAU,CAAC,CAAC;IAgDvF,SAAS,CAAC,UAAU;IAOpB,OAAO,CAAC,oBAAoB;IAc5B,SAAS,CAAC,UAAU,CAAC,KAAK,EAAE,YAAY,GAAG,IAAI;IAsCxC,WAAW,CAAC,KAAK,CAAC,EAAE,eAAe;IAI1C,SAAS,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE,eAAe;CAqB9C"}