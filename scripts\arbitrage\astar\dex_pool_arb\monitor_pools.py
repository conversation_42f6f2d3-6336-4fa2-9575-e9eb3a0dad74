#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
DEX池子价格监控脚本
监控两个WASTR/USDT池子的价格差异，分析套利机会
"""

import os
import sys
import json
import asyncio
import argparse
from decimal import Decimal
from datetime import datetime
from typing import Dict, Any, Optional, Tuple

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../..'))
sys.path.insert(0, project_root)

from scripts.dex.local_swap_simulator import LocalSwapSimulator
from src.utils.logger import logger

# 导入套利分析函数
from analyze_profit import find_best_opportunity

# 池子配置
POOLS = {
    "pool1": {
        "address": "0x806f746a7c4293092ac7aa604347BE123322dF1e",  # ArthSwap WASTR-USDT Pool 1
        "name": "ArthSwap Pool1 WASTR/USDT",
        "token0": "USDT",
        "token1": "WASTR"
    },
    "pool2": {
        "address": "0xDdeA1b3343c438c2E2d636D070cfb4F63d26636e",  # ArthSwap WASTR-USDT Pool 2
        "name": "ArthSwap Pool2 WASTR/USDT",
        "token0": "WASTR",
        "token1": "USDT"
    }
}

# 监控参数
DEFAULT_INTERVAL = 10   # 默认监控间隔（秒）
OPPORTUNITIES_FILE = "dex_arbitrage_opportunities.json"  # 套利机会保存文件

def save_opportunity(opportunity: Dict[str, Any]):
    """
    保存套利机会到文件
    
    Args:
        opportunity: 套利机会详情
    """
    try:
        # 添加时间戳
        opportunity["timestamp"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # 将Decimal转换为float以便JSON序列化
        opportunity = {k: float(v) if isinstance(v, Decimal) else v 
                      for k, v in opportunity.items()}
        
        # 以追加模式写入文件
        with open(OPPORTUNITIES_FILE, "a", encoding="utf-8") as f:
            json.dump(opportunity, f, ensure_ascii=False)
            f.write("\n")
            
        logger.info(f"套利机会已保存到 {OPPORTUNITIES_FILE}")
        
    except Exception as e:
        logger.error(f"保存套利机会时出错: {e}")

async def monitor_pools(interval: int = DEFAULT_INTERVAL):
    """
    监控池子价格并分析套利机会
    
    Args:
        interval: 监控间隔（秒）
    """
    logger.info(f"开始监控池子价格和分析套利机会，间隔 {interval} 秒")
    logger.info(f"套利机会将保存到: {OPPORTUNITIES_FILE}")
    
    while True:
        try:
            # 分析套利机会
            pool_status, opportunity = find_best_opportunity()
            
            if not pool_status:
                logger.warning("无法获取池子状态")
                await asyncio.sleep(interval)
                continue
                
            # 显示池子状态
            logger.info("\n=== 池子状态 ===")
            for pool_id, status in pool_status.items():
                logger.info(f"\n{status['name']}:")
                if pool_id == "pool1":
                    logger.info(f"USDT储备: {status['reserves'][0]:.2f}")
                    logger.info(f"WASTR储备: {status['reserves'][1]:.2f}")
                else:
                    logger.info(f"WASTR储备: {status['reserves'][0]:.2f}")
                    logger.info(f"USDT储备: {status['reserves'][1]:.2f}")
                logger.info(f"当前价格: {status['price']:.6f} WASTR/USDT")
            
            # 如果发现套利机会，保存并显示详情
            if opportunity:
                logger.info("\n=== 发现套利机会! ===")
                logger.info(f"投入金额: {opportunity['amount_usdt']:.2f} USDT")
                logger.info(f"买入池子: {opportunity['buy_pool']}")
                logger.info(f"卖出池子: {opportunity['sell_pool']}")
                logger.info(f"买入价格: {opportunity['buy_price']:.6f} WASTR/USDT")
                logger.info(f"卖出价格: {opportunity['sell_price']:.6f} WASTR/USDT")
                logger.info(f"价差: {opportunity['price_diff']:.2f}%")
                logger.info(f"\n交易详情:")
                logger.info(f"USDT -> WASTR: {opportunity['wastr_amount']:.6f}")
                logger.info(f"WASTR -> USDT: {opportunity['received_usdt']:.6f}")
                logger.info(f"\n利润分析:")
                logger.info(f"毛利润: {opportunity['gross_profit']:.6f} USDT")
                logger.info(f"Gas成本: {opportunity['gas_cost']:.6f} USDT")
                logger.info(f"净利润: {opportunity['net_profit']:.6f} USDT")
                
                # 保存套利机会
                save_opportunity(opportunity)
            else:
                logger.info("\n当前无套利机会")
            
            # 等待下一次监控
            await asyncio.sleep(interval)
            
        except Exception as e:
            logger.error(f"监控出错: {e}")
            await asyncio.sleep(interval)

async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='监控DEX池子价格并分析套利机会')
    parser.add_argument('-i', '--interval', type=int, default=DEFAULT_INTERVAL,
                      help=f'监控间隔（秒），默认{DEFAULT_INTERVAL}秒')
    
    args = parser.parse_args()
    await monitor_pools(args.interval)

if __name__ == "__main__":
    # 在Windows上使用SelectorEventLoop
    if sys.platform == 'win32':
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
    
    # 设置日志编码为utf-8
    import codecs
    sys.stdout = codecs.getwriter('utf-8')(sys.stdout.buffer)
    
    asyncio.run(main()) 