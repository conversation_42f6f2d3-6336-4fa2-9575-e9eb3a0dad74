{"version": 3, "file": "formats.js", "sourceRoot": "", "sources": ["../../src/formats.ts"], "names": [], "mappings": "AAkBA,OAAO,EAAE,SAAS,EAAE,MAAM,yBAAyB,CAAC;AACpD,OAAO,EAAE,aAAa,EAAE,kBAAkB,EAAE,UAAU,EAAE,MAAM,uBAAuB,CAAC;AACtF,OAAO,EAAE,OAAO,EAAE,MAAM,uBAAuB,CAAC;AAChD,OAAO,EAAE,SAAS,EAAE,MAAM,yBAAyB,CAAC;AACpD,OAAO,EAAE,OAAO,EAAE,MAAM,uBAAuB,CAAC;AAChD,OAAO,EAAE,cAAc,EAAE,MAAM,wBAAwB,CAAC;AACxD,OAAO,EAAE,WAAW,EAAE,QAAQ,EAAE,MAAM,wBAAwB,CAAC;AAC/D,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,yBAAyB,CAAC;AAElE,MAAM,OAAO,GAAkD;IAC9D,OAAO,EAAE,CAAC,IAAa,EAAE,EAAE,CAAC,SAAS,CAAC,IAAuB,CAAC;IAC9D,KAAK,EAAE,CAAC,IAAa,EAAE,EAAE,CAAC,OAAO,CAAC,IAAuB,CAAC;IAC1D,WAAW,EAAE,CAAC,IAAa,EAAE,EAAE,CAAC,aAAa,CAAC,IAAgC,CAAC;IAC/E,QAAQ,EAAE,CAAC,IAAa,EAAE,EAAE,CAAC,UAAU,CAAC,IAAc,CAAC;IACvD,gBAAgB,EAAE,CAAC,IAAa,EAAE,EAAE,CAAC,kBAAkB,CAAC,IAAgC,CAAC;IACzF,IAAI,EAAE,CAAC,IAAa,EAAE,EAAE,CAAC,SAAS,CAAC,IAAuB,CAAC;IAC3D,KAAK,EAAE,CAAC,IAAa,EAAE,EAAE,CAAC,OAAO,CAAC,IAA+C,CAAC;IAClF,MAAM,EAAE,CAAC,IAAa,EAAE,EAAE,CAAC,cAAc,CAAC,IAAc,CAAC;IACzD,GAAG,EAAE,CAAC,IAAa,EAAE,EAAE,CAAC,WAAW,CAAC,IAAuB,CAAC;IAC5D,IAAI,EAAE,CAAC,IAAa,EAAE,EAAE,CAAC,MAAM,CAAC,IAAuB,CAAC;IACxD,GAAG,EAAE,CAAC,IAAa,EAAE,EAAE,CAAC,KAAK,CAAC,IAAuB,CAAC;IACtD,MAAM,EAAE,CAAC,IAAa,EAAE,EAAE,CAAC,QAAQ,CAAC,IAAuB,CAAC;IAC5D,MAAM,EAAE,CAAC,IAAa,EAAE,EAAE,CAAC,QAAQ,CAAC,IAAuB,CAAC;CAC5D,CAAC;AACF,yCAAyC;AACzC,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,OAAO,IAAI,GAAG,EAAE,OAAO,IAAI,CAAC,EAAE;IACnD,OAAO,CAAC,MAAM,OAAO,EAAE,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,IAAuB,EAAE,EAAE,OAAO,EAAE,CAAC,CAAC;IAC/E,OAAO,CAAC,OAAO,OAAO,EAAE,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC,IAAuB,EAAE,EAAE,OAAO,EAAE,CAAC,CAAC;CACjF;AACD,iBAAiB;AACjB,KAAK,IAAI,IAAI,GAAG,CAAC,EAAE,IAAI,IAAI,EAAE,EAAE,IAAI,IAAI,CAAC,EAAE;IACzC,OAAO,CAAC,QAAQ,IAAI,EAAE,CAAC,GAAG,IAAI,CAAC,EAAE,CAChC,OAAO,CAAC,IAA+C,EAAE,EAAE,IAAI,EAAE,CAAC,CAAC;CACpE;AACD,OAAO,CAAC,QAAQ,GAAG,OAAO,CAAC,KAAK,CAAC;AAEjC,eAAe,OAAO,CAAC"}